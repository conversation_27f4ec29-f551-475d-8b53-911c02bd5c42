'use client';
import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import SkinPreview from '@/components/skin/SkinPreview';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import { ButtonIcon } from '@/components/Button';
import Tooltip from '@/components/Tooltip';
import {
  selectIsSkinModalOpen,
  selectLayoutBackground,
  setIsSkinModalOpen,
} from '@/store/features/diarySlice';
import EssayFeedBackModal from '../../essay/_components/FeedbackModal';
import SelectSkinModal from '../../diary/_component/SelectSkinModal';
import CorrectionSection from '../_components/CorrectionSection';

const WriteNovel = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { id } = useParams();

  const [isSaving, setIsSaving] = useState(false);
  const [showError, setShowError] = useState(false);
  const isSkinModalOpen = useSelector(selectIsSkinModalOpen);
  const [selectedSkin, setSelectedSkin] = useState(null);
  const layoutBackground = useSelector(selectLayoutBackground);

  const [subject, setSubject] = useState('');
  const [body, setBody] = useState('');
  const [wordCount, setWordCount] = useState(0); // Single declaration
  const [date, setDate] = useState(new Date().toISOString().slice(0, 10));

  const {
    data: novelDetails,
    isLoading,
    refetch,
  } = useDataFetch({
    queryKey: ['novel-details', id],
    endPoint: `/student/novel/entries/topic/${id}`,
  });

  useEffect(() => {
    if (!isLoading) {
      setSelectedSkin(novelDetails?.skin);
      setSubject(novelDetails?.topic?.title);
      setBody(novelDetails?.content);
      setDate(
        novelDetails?.submissionDate
          ? new Date(novelDetails?.submissionDate).toISOString().slice(0, 10)
          : new Date().toISOString().slice(0, 10)
      );
    }
  }, [novelDetails]);

  const countWords = (html) => {
    if (!html) return 0;
    const text = html.replace(/<[^>]*>/g, ' ');
    const cleanText = text.replace(/&nbsp;|&|<|>|"|'/g, ' ');
    return cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0).length;
  };

  useEffect(() => {
    setWordCount(countWords(body));
  }, [body]);

  const handleSave = async () => {
    const payload = {
      entryId: novelDetails?.id,
      // skinId: essayId,
      skinId: selectedSkin?.id || null,
      backgroundColor: layoutBackground,
      //   title: subject,
      content: body,
    };
    console.log(payload);

    try {
      setIsSaving(true);
      const response = await api.post('/student/novel/entries/submit', payload);
      refetch();
      setShowError(false);
      router.push('/novel');
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdate = async () => {
    const payload = {
      skinId: selectedSkin?.id || null,
      backgroundColor: layoutBackground,
      //   title: subject,
      content: body,
    };
    console.log(payload);

    try {
      setIsSaving(true);
      const response = await api.put(
        `/student/novel/entries/${novelDetails?.id}`,
        payload
      );
      refetch();
      setShowError(false);
      router.push('/novel');
    } catch (error) {
      console.error('Error saving:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSkinChange = async (
    newSkin,
    dispatch,
    setSelectedSkin,
    setSubject,
    setMessage
  ) => {
    if (!newSkin.templateContent) {
      toast.error('This skin has no valid template content');
      return;
    }

    try {
      // 1. First reset everything
      setSubject('');
      setMessage('');

      // // 3. Parse and apply template
      // const data = JSON.parse(newSkin.templateContent);
      setSelectedSkin(newSkin);

      toast.success(`Skin "${newSkin.name}" applied successfully`);
    } catch (error) {
      console.error('Error applying skin template:', error);
      toast.error('Failed to apply skin template');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto p-5 xl:px-0 my-8 relative">
      <div className="p-2 bg-[#FDE7E9] grid grid-cols-1 lg:grid-cols-2 gap-3 rounded">
        <div className="bg-white rounded-lg text-center flex items-center relative py-5">
          <div className="space-y-5">
            {/* {console.log(novelDetails?.skin?.templateContent,'dif', selectedSkin.templateContent, 'HELLO')} */}
            <SkinPreview
              skin={
                selectedSkin
                  ? selectedSkin?.templateContent
                  : novelDetails?.skin?.templateContent
              }
              contentData={{
                subject,
                body,
                date,
              }}
            />

            <button
              onClick={
                novelDetails?.status === 'submitted' ? handleUpdate : handleSave
              }
              disabled={isSaving}
              className={`  text-black font-medium py-2 px-8  text-center rounded-full whitespace-nowrap
                border-2 border-yellow-100
                shadow-[0px_4px_6px_-1px_rgba(0,0,0,0.1),0px_2px_4px_-2px_rgba(0,0,0,0.1),-4px_8px_12px_0px_#00000026]
                transition-all duration-300
                bg-gradient-to-b from-yellow-300 to-yellow-500 hover:from-yellow-400 hover:to-yellow-600
                relative pr-8
                ring-2 ring-[#A36105] ${
                  isSaving
                    ? 'bg-gray-300 cursor-not-allowed'
                    : 'bg-yellow-400 hover:bg-yellow-300'
                }`}
            >
              {isSaving
                ? novelDetails?.status === 'submitted'
                  ? 'Updating...'
                  : 'Saving...'
                : novelDetails?.status === 'submitted'
                ? 'Update'
                : 'Submit'}
            </button>
          </div>
        </div>

        {/* Example inputs for subject, body, and date */}
        <div className=" bg-white rounded-lg p-3 space-y-3 w-full">
          <div className="flex flex-col p-2 shadow-lg border rounded-lg gap-3">
            <div className="flex items-center justify-between border-b border-dashed pb-1 gap-3">
              <h2>{novelDetails?.topic?.title}</h2>
              <span className="min-w-24">{date}</span>
            </div>
            <textarea
              value={body}
              onChange={(e) => setBody(e.target.value)}
              placeholder="Essay"
              className={`border min-h-60 ${
                showError && 'border-red-500'
              } p-2 w-full rounded focus:outline-[1px] focus:outline-gray-400 shadow-[inset_2px_2px_6px_0px_#0000001F]`}
              rows={4}
            />

            {/* <div className="text-sm flex items-center justify-between">
              <span>{`${wordCount} / ${
                submissionDetails?.task?.wordLimitMaximum || Infinity
              } (word)`}</span>

              {showError &&
                wordCount < submissionDetails?.task?.wordLimitMinimum && (
                  <span className="text-red-500">
                    Minimum {submissionDetails?.task?.wordLimitMinimum} words
                    required.
                  </span>
                )}
            </div> */}
          </div>

          <CorrectionSection
            subject={subject}
            date={date}
            id={novelDetails?.id}
          />
        </div>
      </div>


      <div className="absolute top-5 -right-10">
        <div className="w-8 h-8 cursor-pointer">
          <Tooltip
            content={'Skin'}
            color="user"
            size="lg"
            delay={100}
            className="-ml-3 "
            position="right"
          >
            <ButtonIcon
              icon={'arcticons:image-combiner'}
              innerBtnCls="h-12 w-12"
              btnIconCls="h-5 w-5"
              aria-label={'Skin'}
              onClick={() => dispatch(setIsSkinModalOpen(true))}
            />
          </Tooltip>
        </div>
      </div>

      <SelectSkinModal
        isOpen={isSkinModalOpen}
        onClose={() => dispatch(setIsSkinModalOpen(false))}
        onApply={(skin) =>
          handleSkinChange(
            skin,
            dispatch,
            (skin) => setSelectedSkin(skin),
            (content) => setSubject(content),
            (content) => setBody(content)
          )
        }
        currentSkinId={novelDetails?.skin?.id}
      />
    </div>
  );
};

export default WriteNovel;
