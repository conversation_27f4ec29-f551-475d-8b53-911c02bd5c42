'use client';

import NewTablePage from '@/components/form/NewTablePage';
import EditQuestionModal from '../edit-question-modal/page';
import DeleteModal from '@/components/form/modal/DeleteModal';
import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import api from '@/lib/api';
import { useSelector } from 'react-redux';

const QuestionBank = () => {
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [questions, setQuestions] = useState([]);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();
  const auth = useSelector((state) => state.auth);
  const getQAQuestionListEndpoint = (roles) =>
    roles?.includes('tutor') ? '/tutor/qa/questions' : '/admin/qa/questions';

  const getQAQuestionDetailEndpoint = (roles, id) =>
    roles?.includes('tutor')
      ? `/tutor/qa/questions/${id}`
      : `/admin/qa/questions/${id}`;
  // State for the edit modal
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [currentQuestionId, setCurrentQuestionId] = useState(null);

  // Add new state for delete modal and selected question data
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedQuestion, setSelectedQuestion] = useState(null);
  const [fetchingQuestion, setFetchingQuestion] = useState(false);

  // Fetch questions
  const fetchQuestions = async () => {
    try {
      setIsLoading(true);

      const response = await api.get(
        `${getQAQuestionListEndpoint(
          auth?.user?.roles
        )}?page=${currentPage}&limit=${rowsPerPage}`
      );
      console.log('Raw API response:', response);

      // Improved data extraction to handle different response structures
      if (response?.data) {
        let items = [];
        let totalCount = 0;
        let totalPagesCount = 0;

        // Check different possible data structures
        if (response.data.items && Array.isArray(response.data.items)) {
          // Direct items array at top level
          items = response.data.items;
          totalCount =
            response.data.totalCount || response.data.totalItems || 0;
          totalPagesCount = response.data.totalPages || 0;
        } else if (response.data.data) {
          // Items in nested data property
          if (
            response.data.data.items &&
            Array.isArray(response.data.data.items)
          ) {
            items = response.data.data.items;
            totalCount =
              response.data.data.totalCount ||
              response.data.data.totalItems ||
              0;
            totalPagesCount = response.data.data.totalPages || 0;
          } else if (Array.isArray(response.data.data)) {
            // Direct array in data property
            items = response.data.data;
            totalCount = items.length;
            totalPagesCount = 1;
          }
        } else if (Array.isArray(response.data)) {
          // Response data is directly an array
          items = response.data;
          totalCount = items.length;
          totalPagesCount = 1;
        }

        if (items.length > 0) {
          const formattedQuestions = items.map((item) => ({
            id: item.id,
            title: item.question,
            points: item.points,
            minimumWords: item.minimumWords,
          }));

          setQuestions(formattedQuestions);
          setTotalItems(totalCount);
          setTotalPages(totalPagesCount);
          console.log('Formatted questions:', formattedQuestions);
          setIsError(false);
          setErrorMessage('');
        } else {
          console.log('No questions found in response');
          setQuestions([]);
          setTotalItems(0);
          setTotalPages(0);
          setIsError(false);
          setErrorMessage('');
        }
      } else {
        console.error('Unexpected data structure:', response);
        setIsError(true);
        setErrorMessage('Unexpected data structure received from API');
        setQuestions([]);
      }
    } catch (err) {
      console.error('Error fetching questions:', err);
      setIsError(true);
      setErrorMessage(
        err.message || 'An error occurred while fetching questions'
      );
      setQuestions([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchQuestions();
  }, [currentPage, rowsPerPage]);

  // Handle edit question
  const handleEditQuestion = (row) => {
    setCurrentQuestionId(row.id);
    setIsEditModalOpen(true);
  };

  // Handle close modal and reset form
  const handleCloseModal = () => {
    setIsEditModalOpen(false);
    setCurrentQuestionId(null);
  };

  // Updated delete question handler to use DeleteModal
  const handleDeleteQuestion = async (row) => {
    // Check if a deletion is already in progress
    if (isDeleting) return;

    try {
      setFetchingQuestion(true);
      // Set the selected question data for the modal
      setSelectedQuestion(row);
      setShowDeleteModal(true);
    } catch (error) {
      console.error('Error preparing delete modal:', error);
    } finally {
      setFetchingQuestion(false);
    }
  };

  // Define columns for the table
  const columns = [
    {
      label: 'QUESTION',
      field: 'title',
    },
    {
      label: 'POINTS',
      field: 'points',
    },
    {
      label: 'MINIMUM WORDS',
      field: 'minimumWords',
    },
  ];

  // Define actions
  const actions = [
    {
      name: 'edit',
      icon: 'material-symbols:edit',
      className: 'text-black-600',
      onClick: handleEditQuestion,
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: handleDeleteQuestion,
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Question Bank"
        createButton="Add Question"
        onCreateClick={() => router.push('/admin/questions/create')}
        columns={columns}
        data={questions}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={totalPages}
        loading={isLoading}
        error={isError}
        errorMessage={errorMessage}
        showCheckboxes={false}
        showSearch={false}
        searchPlaceholder="Search questions..."
        showNameFilter={false}
        showSortFilter={false}
        showCreateButton={false}
      />

      {/* Edit Question Modal */}
      <EditQuestionModal
        isOpen={isEditModalOpen}
        onClose={handleCloseModal}
        questionId={currentQuestionId}
        onQuestionUpdated={fetchQuestions}
      />

      {/* DeleteModal Integration */}
      {showDeleteModal && (
        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          data={selectedQuestion}
          endPoint={getQAQuestionDetailEndpoint(
            auth?.user?.roles,
            selectedQuestion?.id
          )}
          onSuccess={fetchQuestions}
        />
      )}
    </div>
  );
};

export default QuestionBank;
