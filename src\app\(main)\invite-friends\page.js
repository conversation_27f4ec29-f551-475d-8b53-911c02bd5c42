'use client';
import GoBack from '@/components/shared/GoBack';
import FriendList from './_components/FriendList';
import { useDispatch, useSelector } from 'react-redux';
import { setFriendActiveTab } from '@/store/features/commonSlice';
import RequestList from './_components/RequestList';
import SearchFriend from './_components/SearchFriend';

const InviteFriends = () => {
  const { friendActiveTab: activeTab } = useSelector((state) => state.common);

  const dispatch = useDispatch();

  return (
    <div className="max-w-7xl mx-auto px-5 xl:px-0 py-5">
      <GoBack title="Invite Friends" />

      <div className="shadow-lg border border-sky-200 max-sm:p-5 p-10 mt-5 rounded-lg h-full">
        <div className="max-w-4xl mx-auto">
          <SearchFriend />
        </div>
      </div>
    </div>
  );
};

export default InviteFriends;
