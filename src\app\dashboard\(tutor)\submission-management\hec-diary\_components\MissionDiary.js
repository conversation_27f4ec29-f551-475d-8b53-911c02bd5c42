'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { formatDate } from '@/utils/dateFormatter';

const MissionDiary = () => {
  const router = useRouter();

  // State variables
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('studentName');
  const [sortField, setSortField] = useState('submittedAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Fetch mission diary submissions using useDataFetch hook
  const {
    data: diaryData,
    isLoading: loading,
    error
  } = useDataFetch({
    queryKey: ['tutor-mission-diary-pending-reviews', currentPage, rowsPerPage, sortField, sortDirection],
    endPoint: '/diary/tutor/missions/entries',
    params: {
      page: currentPage,
      limit: rowsPerPage,
      sortBy: sortField,
      sortDirection: sortDirection
    }
  });

  // Log any errors
  if (error) {
    console.error('Error fetching mission diary data:', error);
  }

  // Extract data from the API response
  const submissions = diaryData?.items || [];
  const totalItems = diaryData?.meta?.totalItems || 0;
  const totalPages = diaryData?.meta?.totalPages || 1;

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1);
  };

  // Handle sort
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Handle view submission
  const handleViewSubmission = (submission) => {
    router.push(`/dashboard/submission-management/hec-diary/review/mission/${submission.id}?tab=missionDiary`);
  };

  // Define table columns
  const columns = [
    { field: 'studentName', label: 'STUDENT NAME', sortable: false },
    
    {
      field: 'status',
      label: 'REVIEW STATUS',
      sortable: false,
      cellRenderer: (_, row) => {
        const isReviewed = row.reviewedByCurrentTutor || row.underReviewByOtherTutor;
        return (
          <div className="flex items-center">
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${
              isReviewed
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              {isReviewed ? 'Reviewed' : 'Not Reviewed Yet'}
            </span>
            {!isReviewed && (
              <span className="ml-2 text-red-500">✕</span>
            )}
            {isReviewed && (
              <span className="ml-2 text-green-500">✓</span>
            )}
          </div>
        );
      }
    },
    // {
    //   field: 'title',
    //   label: 'MESSAGE',
    //   sortable: true,
    //   cellRenderer: (value) => (
    //     <div className="truncate max-w-xs">
    //       {value || "No message"}
    //     </div>
    //   )
    // }
  ];

  // Define actions for table rows
  const actions = [
    {
      icon: 'heroicons-outline:eye',
      className: 'text-blue-600 hover:text-blue-700 cursor-pointer',
      onClick: handleViewSubmission
    }
  ];

  // Define search filter options
  const searchFilterOptions = [
    { label: 'Student Name', value: 'studentName' },
    { label: 'Mission', value: 'missionTitle' },
    { label: 'Title', value: 'title' }
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      

      {/* Show loading or error states */}
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
          Error loading data: {error.message}
        </div>
      )}

      {/* Table */}
      <NewTablePage
        columns={columns}
        data={submissions}
        actions={actions}
        loading={loading}
        title='Mission Diary Submissions'
        // Pagination props
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        showCheckboxes={false}

        // Search and filter props
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}

        // Pass current state values
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}

        // Pass handlers for search, filter and sort
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}

        // Pass name filter options
        nameFilterOptions={searchFilterOptions}
      />
    </div>
  );
};

export default MissionDiary;
