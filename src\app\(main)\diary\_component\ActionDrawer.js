'use client'
import { useState, useRef } from 'react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import Tooltip from '@/components/Tooltip'
import ShareModalContent from './modalContents/ShareModalContent'
import EmojiSelectorModal from './EmojiSelectorModal'

const ActionDrawer = ({ isOpen, entry, onFeedback }) => {
  const router = useRouter()
  const decorationButtonRef = useRef(null)

  const [showShareModal, setShowShareModal] = useState(false)
  const [isEmojiSelectorOpen, setIsEmojiSelectorOpen] = useState(false)

  const handleLike = () => {}

  const handleEmojiSelect = (emoji) => {
    /* handle selected emoji */
  }

  const items = [
    {
      icon: '/assets/images/all-img/friends.svg',
      label: 'Friends',
      onClick: () => router.push('/find-friends'),
    },
    {
      icon: '/assets/images/all-img/decoration.svg',
      label: 'Decorate',
      onClick: () => setIsEmojiSelectorOpen(true),
      ref: decorationButtonRef,
    },
    {
      icon: '/assets/images/all-img/like.svg',
      label: 'Like',
      onClick: handleLike,
      count: entry?.likeCount ?? 0,
    },
    {
      icon: '/assets/images/all-img/share.svg',
      label: 'Share',
      onClick: () => setShowShareModal(true),
    },
    {
      icon: '/assets/images/all-img/message.svg',
      label: 'Feedback',
      onClick: onFeedback,
    },
    {
      icon: '/assets/images/all-img/edit-button.svg',
      label: 'Edit Diary',
      onClick: () => {
        // Navigate to diary page with entry ID
        if (entry?.id) {
          router.push(`/diary?entryId=${entry.id}`)
        }
      },
    },
  ]

  return (
    <>
      <div
        className={`absolute bottom-[12px] left-2 flex items-center gap-2 px-4 py-1 rounded-full shadow-lg transition-all duration-500 ease-in-out ${
          isOpen ? 'translate-x-0 opacity-100' : '-translate-x-60 opacity-0 pointer-events-none'
        }`}
        style={{
          background: 'linear-gradient(180deg, #FBD53F 0%, #DFA209 100%)',
        }}
      >
        <div className="flex items-center gap-2 ml-10">
          {items.map(({ icon, label, onClick, count, ref }, index) => {
            const tooltip =
              count !== undefined && count !== null
                ? `${count}${label.toLowerCase()}`
                : label
            return (
              <Tooltip key={index} content={tooltip}>
                <button
                  ref={ref ? ref : null}
                  onClick={onClick}
                  className="w-10 h-10 flex items-center justify-center"
                >
                  <Image src={icon} alt={label} width={50} height={50} />
                </button>
              </Tooltip>
            )
          })}
        </div>
      </div>

      {showShareModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <div className="bg-white w-full max-w-lg rounded-xl overflow-auto max-h-[90vh]">
            <div className="flex justify-end p-2">
              <button
                onClick={() => setShowShareModal(false)}
                className="text-2xl leading-none"
              >
                &times;
              </button>
            </div>
            <ShareModalContent entryId={entry?.id} />
          </div>
        </div>
      )}

      <EmojiSelectorModal
        isOpen={isEmojiSelectorOpen}
        onClose={() => setIsEmojiSelectorOpen(false)}
        onSelect={handleEmojiSelect}
        triggerRef={decorationButtonRef}
        position="left"
      />
    </>
  )
}

export default ActionDrawer
