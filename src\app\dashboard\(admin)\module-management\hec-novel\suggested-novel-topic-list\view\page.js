'use client';

import React, { useRef, useEffect } from 'react';
import Image from 'next/image';
import { toast } from 'sonner';
import api from '@/lib/api';

const NovelViewModal = ({ 
  showModal, 
  selectedNovel, 
  modalLoading, 
  modalError, 
  onClose, 
  onRefreshList 
}) => {
  const modalRef = useRef(null);

  useEffect(() => {
    if (!showModal) return;

    // Handle escape key press
    const handleEscapeKey = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Handle click outside
    const handleClickOutside = (e) => {
      if (modalRef.current && !modalRef.current.contains(e.target)) {
        onClose();
      }
    };

    // Add event listeners
    document.addEventListener('keydown', handleEscapeKey);
    document.addEventListener('mousedown', handleClickOutside);
    
    // Prevent body scrolling when modal is open
    document.body.style.overflow = 'hidden';

    // Cleanup
    return () => {
      document.removeEventListener('keydown', handleEscapeKey);
      document.removeEventListener('mousedown', handleClickOutside);
      // Restore body scrolling when modal is closed
      document.body.style.overflow = 'auto';
    };
  }, [showModal, onClose]);

  // Handle status update
  const handleStatusUpdate = async (newStatus) => {
    try {
      const endpoint = `/admin/novel/suggestions/${selectedNovel.id}/status`;
      await api.patch(endpoint, { status: newStatus });
      
      toast.success(`Status updated to ${newStatus}`);
      
      // Call the refresh function passed from parent
      if (onRefreshList) {
        onRefreshList();
      }
      
      // Close modal after status update
      onClose();
    } catch (err) {
      console.error('Error updating status:', err);
      toast.error('Failed to update status');
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge color
  const getStatusBadgeColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!showModal) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden relative"
      >
        {/* Close button - Round orange/amber with X */}
        <div className="absolute top-4 right-4 z-10">
          <button
            type="button"
            onClick={onClose}
            className="focus:outline-none"
            aria-label="Close"
          >
            <Image 
              src="/assets/images/all-img/cross-bg.png" 
              alt="Close" 
              width={40} 
              height={40} 
              className="w-10 h-10" 
            />
          </button>
        </div>
        
        {/* Wooden Sign Header */}
        <div className="bg-[#FFF9FB] pt-6 pb-8 px-6 border-b border-amber-200">
          <div className="flex justify-center mb-2">
            <div className="relative w-full max-w-md">
              {/* Wooden sign background - using novel/book related image */}
              <Image
                src="/assets/images/all-img/novel-suggestion-bg.png"
                alt="Novel Suggestion"
                width={600}
                height={200}
                className="w-full h-auto"
                priority
              />
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="px-8 py-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {modalLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
              <span className="ml-2 text-gray-600">Loading novel details...</span>
            </div>
          ) : modalError ? (
            <div className="text-center py-6">
              <p className="text-red-600 mb-4">{modalError}</p>
            </div>
          ) : selectedNovel ? (
            <div className="space-y-6">
              {/* Basic Information - Fixed Layout */}
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                  {/* Student Name */}
                  <div>
                    <h3 className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">
                      Student Name
                    </h3>
                    <p className="text-lg text-gray-900">
                      {selectedNovel.userName || 'N/A'}
                    </p>
                  </div>

                  {/* Created At */}
                  <div>
                    <h3 className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">
                      Created At
                    </h3>
                    <p className="text-lg text-gray-900">
                      {formatDate(selectedNovel.createdAt)}
                    </p>
                  </div>

                  {/* Last Updated - spans full width on smaller screens, aligns with Student Name on larger screens */}
                  <div className="md:col-span-2">
                    <h3 className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-2">
                      Last Updated
                    </h3>
                    <p className="text-lg text-gray-900">
                      {formatDate(selectedNovel.updatedAt)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Description Section */}
              <div className="pt-4 border-t border-gray-100">
                <h3 className="text-sm font-semibold text-gray-600 uppercase tracking-wide mb-4">
                  Description
                </h3>
                <div className="prose max-w-none">
                  <p className="text-gray-900 leading-relaxed whitespace-pre-wrap text-lg">
                    {selectedNovel.description || 'No description provided'}
                  </p>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default NovelViewModal;