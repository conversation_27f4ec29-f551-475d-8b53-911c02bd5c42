import React from 'react';
import { ButtonIcon } from '@/components/Button';

const DiaryPagination = ({
  hasEntries,
  isOpen,
  currentIndex,
  totalEntries,
  onLeftClick,
  onRightClick
}) => {
  // Determine if navigation arrows should be disabled
  const isLeftDisabled = currentIndex === 0 || !hasEntries || !isOpen;
  const isRightDisabled = !hasEntries || (isOpen && currentIndex + 2 >= totalEntries);

  return (
    <>
      {hasEntries && (
        <>
          <div className="absolute bottom-[15px] left-[-50px]">
            <div className="w-8 h-8">
              <ButtonIcon
                icon="mdi:arrow-left"
                innerBtnCls={`h-12 w-12 ${isLeftDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                btnIconCls="h-5 w-5"
                aria-label="left navigation"
                onClick={!isLeftDisabled ? onLeftClick : undefined}
              />
            </div>
          </div>

          <div className="absolute bottom-[15px] right-[-50px]">
            <div className="w-8 h-8">
              <ButtonIcon
                icon="mdi:arrow-right"
                innerBtnCls={`h-12 w-12 ${isRightDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                btnIconCls="h-5 w-5"
                aria-label="right navigation"
                onClick={!isRightDisabled ? onRightClick : undefined}
              />
            </div>
          </div>

          {isOpen && (
            <div className="absolute -bottom-5 left-1/2 transform -translate-x-1/2 text-xs bg-white px-3 py-1 rounded-full shadow-sm border border-gray-200">
              {currentIndex + 1 < totalEntries ? (
                <>Page {currentIndex + 1}-{Math.min(currentIndex + 2, totalEntries)} of {totalEntries}</>
              ) : (
                <>Page {currentIndex + 1} of {totalEntries}</>
              )}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default DiaryPagination;
