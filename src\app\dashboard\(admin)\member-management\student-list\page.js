'use client';

import NewTablePage from "@/components/form/NewTablePage";
import useDataFetch from "@/hooks/useDataFetch";
import StudentViewModal from "./view/page"; // Import the StudentViewModal
import { useRouter } from 'next/navigation';
import React, { useState, useCallback } from 'react';
import { debounce } from 'lodash';

// Helper function to map partial gender inputs to valid API values
const mapGenderInput = (input) => {
  if (!input) return '';
  
  const lowerInput = input.toLowerCase().trim();
  
  // Match "male" variations
  if (['m', 'ma', 'mal', 'male'].includes(lowerInput)) {
    return 'male';
  }
  
  // Match "female" variations
  if (['f', 'fe', 'fem', 'fema', 'femal', 'female'].includes(lowerInput)) {
    return 'female';
  }
  
  // Match "other" variations
  if (['o', 'ot', 'oth', 'othe', 'other'].includes(lowerInput)) {
    return 'other';
  }
  
  return ''; // Return empty if no match
};

const studentList = () => {
  const router = useRouter();
  
  // State for student view modal
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  
  // Handle back button click
  const handleBackClick = () => {
    router.back();
  };
  
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10); // Changed to 10 for better visibility
  
  // State for search and sorting
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('userId');
  const [sortField, setSortField] = useState('userId');
  const [sortDirection, setSortDirection] = useState('DESC');
  
  // Create debounced search function
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((term) => {
      setSearchTerm(term);
      setCurrentPage(1); // Reset to first page on new search
    }, 300),
    []
  );
  
  // Prepare query parameters for API request
  const prepareQueryParams = useCallback(() => {
    const params = {
      page: currentPage,
      limit: rowsPerPage,
      sortBy: sortField,
      sortDirection: sortDirection
    };
    
    // Only add search term if it's not empty
    if (searchTerm && searchTerm.trim() !== '') {
      // Special handling for gender field
      if (searchField === 'gender') {
        const mappedGender = mapGenderInput(searchTerm);
        if (mappedGender) {
          params[searchField] = mappedGender;
        }
        // If no valid gender match, don't add the parameter
      } 
      // For userId, handle spaces specially
      else if (searchField === 'userId') {
        const firstWord = searchTerm.split(' ')[0];
        params[searchField] = firstWord;
      } 
      // For other fields, use the full search term
      else {
        params[searchField] = searchTerm.trim();
      }
    }
    
    return params;
  }, [currentPage, rowsPerPage, sortField, sortDirection, searchField, searchTerm]);
  
  // Get the current query parameters
  const queryParams = prepareQueryParams();
  
  // Handler for search input changes
  const handleSearch = (term) => {
    console.log('Search term:', term); // Debug log
    debouncedSearch(term);
  };
  
  // Fetch users data using the custom hook
  const {
    data: response,
    isLoading,
    error
  } = useDataFetch({
    queryKey: ['users/student', queryParams],
    endPoint: '/users/student',
    params: queryParams
  });
  
  // Function to get total count handling different response structures
  const getTotalCount = () => {
    if (!response) return 0;
    
    // First try to get from meta.totalItems (current structure)
    if (response.meta?.totalItems !== undefined) {
      return response.meta.totalItems;
    }
    
    // Fallback to response.totalCount if available
    if (response.totalCount !== undefined) {
      return response.totalCount;
    }
    
    // Last resort: count the items in the array
    return (response.items || []).length;
  };
  
  // Client-side filtering for multi-word searches and exact gender matching
  const filteredUsers = React.useMemo(() => {
    const users = response?.items || [];
    
    // Apply filters based on search conditions
    return users.filter(user => {
      // Handle multi-word name searches
      if (searchTerm && searchTerm.includes(' ') && searchField === 'userId') {
        if (!user.name) return false;
        
        // Case-insensitive matching for full name
        const userName = user.name.toLowerCase();
        const searchTermLower = searchTerm.toLowerCase();
        
        return userName.includes(searchTermLower);
      }
      
      // Handle gender exact matching - we use our smart mapping function
      if (searchField === 'gender' && searchTerm) {
        const mappedGender = mapGenderInput(searchTerm);
        const userGender = (user.gender || '').toLowerCase().trim();
        
        // If we have a valid mapping, match exactly
        if (mappedGender) {
          return userGender === mappedGender;
        }
      }
      
      // Default: include the user in results
      return true;
    });
  }, [response, searchTerm, searchField]);
  
  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  
  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    // Clear search when field changes to avoid confusion
    setSearchTerm('');
  };
  
  // Handle sort change
  const handleSort = (field) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      // Set new field and default to ASC
      setSortField(field);
      setSortDirection('ASC');
    }
  };
  
  // Handle view student modal
  const handleViewStudent = (studentData) => {
    setSelectedStudent(studentData);
    setIsViewModalOpen(true);
  };
  
  const handleCloseViewModal = () => {
    setIsViewModalOpen(false);
    setSelectedStudent(null);
  };
  
  // Handle message button click - navigate to chat app
  const handleMessageStudent = (studentData) => {
    console.log('Navigating to chat for student:', studentData);
    router.push('/chat-app');
  };
  
  // Extract metadata from response
  const totalItems = getTotalCount();
  const totalPages = response?.meta?.totalPages || Math.ceil(totalItems / rowsPerPage) || 1;
  
  // Navigation function for the "Add User" button
  const handleCreateUser = () => {
    router.push('/dashboard/member-management/add-student');
  };
  
  // Define name filter options
  const nameFilterOptions = [
    { label: 'Student ID', value: 'userId' },
    { label: 'Email', value: 'email' },
    { label: 'Phone Number', value: 'phoneNumber' },
    { label: 'Gender', value: 'gender' }
  ];
  
  const UserAvatar = ({ name }) => {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-amber-100 text-amber-800">
        <svg 
          className="h-5 w-5" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    );
  };
  
  // Define columns for the table with sorting
  const columns = [
    {
      label: 'STUDENT NAME',
      field: 'name',
      cellRenderer: (value, row) => (
        <div className="flex items-center space-x-2">
          <UserAvatar name={value} />
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },
    {
      label: 'EMAIL ADDRESS',
      field: 'email',
    },
    {
      label: 'PHONE NUMBER',
      field: 'phoneNumber',
    },
    {
      label: 'GENDER',
      field: 'gender',
    }
  ];
  
  // Define action buttons - updated message action to navigate to chat-app
  const actions = [
    {
      name: 'message',
      icon: 'material-symbols:chat',
      className: 'text-green-600',
      onClick: (row) => handleMessageStudent(row),
    },
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (row) => handleViewStudent(row),
    },
  ];
  
  // Display error message if API call fails
  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error loading users: {error.message}
      </div>
    );
  }
  
  return (
    <div className="w-full px-4">
      <div className="overflow-auto max-h-[80vh]">
        <NewTablePage
          title="Student Management"
          createButton="Add Student"
          columns={columns}
          actions={actions}
          data={filteredUsers} // Use filtered results
          loading={isLoading}
          showCreateButton={true}
          openCreateModal={handleCreateUser}
          onBack={handleBackClick}
          
          // Pagination props
          currentPage={currentPage}
          totalPages={totalPages}
          changePage={handlePageChange}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}

          showCheckboxes={false}
          
          // Search and filter props
          showSearch={true}
          showNameFilter={true}
          showSortFilter={true}
          
          // Pass current state values
          searchTerm={searchTerm}
          searchField={searchField}
          sortField={sortField}
          sortDirection={sortDirection}
          
          // Pass handlers for search, filter and sort
          onSearch={handleSearch}
          onNameFilterChange={handleSearchFieldChange}
          onSort={handleSort}
          
          // Pass name filter options
          nameFilterOptions={nameFilterOptions}
        />
      </div>
      
      {/* Student View Modal */}
      <StudentViewModal
        isOpen={isViewModalOpen}
        onClose={handleCloseViewModal}
        studentData={selectedStudent}
      />
    </div>
  );
};

export default studentList;