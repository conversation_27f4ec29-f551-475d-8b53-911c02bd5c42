'use client';
import React, { useRef, useState, useEffect } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { Icon } from '@iconify/react';
import { motion } from 'framer-motion';
import { useSelector, useDispatch } from 'react-redux';
import {
  addBlank,
  removeBlank,
  setEditorContent,
  setPoints,
  selectBlanks,
  selectEditorContent,
  selectPoints,
} from '@/store/features/hecPlaySlice';
import Button from '../../../_components/Button';
import BlankAnswers from '../../../_components/BlankAnswers';
import { useParams } from 'next/navigation';
import api from '@/lib/api';
import WordBlockInput from '@/components/WordBlockInput';

// Main component using Redux
const QuestionCreatorPage = () => {
  const { id } = useParams();
  const editorRef = useRef(null);
  const dispatch = useDispatch();
  const blanks = useSelector(selectBlanks);
  const editorContent = useSelector(selectEditorContent);
  const points = useSelector(selectPoints);
  const [blankCounter, setBlankCounter] = useState(1);
  const [rawText, setRawText] = useState('');
  const [blocks, setBlocks] = useState([]);

  const handleBlocksChange = (newBlocks) => {
    setBlocks(newBlocks);
  };
  console.log(rawText);

  const handleAddBlank = () => {
    if (editorRef.current) {
      const editor = editorRef.current;
      // Get the current selection/cursor position (not used but kept for clarity)

      // Create the blank node
      const blankId = `blank-${blankCounter}`;
      const blankNode = `<span class="blank-highlight" contenteditable="false" data-blank-id="${blankId}">___<span class="blank-remove" contenteditable="false">×</span></span>`;

      // Get current cursor position
      const selection = editor.selection.getBookmark();

      // Insert at current cursor position
      editor.insertContent(blankNode);

      // Get the updated content as plain text with cursor position restored
      editor.selection.moveToBookmark(selection);

      // Update the raw text with [[gap]] markers
      const updatedContent = editor.getContent({ format: 'text' });
      // Replace "___×" with "[[gap]]" and remove any extra newlines
      const processedText = updatedContent.replace(
        /___[\s\n]*×[\s\n]*/g,
        '[[gap]]'
      );
      setRawText(processedText);

      // Focus back on the editor
      editor.focus();

      // Add blank to state
      dispatch(addBlank(blankId));
      setBlankCounter((prev) => prev + 1);
    }
  };

  const handleEditorChange = (content) => {
    dispatch(setEditorContent(content));

    // Update raw text whenever editor content changes
    if (editorRef.current) {
      const editor = editorRef.current;
      const plainText = editor.getContent({ format: 'text' });
      // Replace "___×" with "[[gap]]" and remove any extra newlines
      const processedText = plainText.replace(/___[\s\n]*×[\s\n]*/g, '[[gap]]');
      setRawText(processedText);
    }
  };

  // Effect to handle synchronization when blanks are removed from the answer section
  useEffect(() => {
    // This effect will run when the blanks array changes
    if (editorRef.current) {
      const editor = editorRef.current;
      const allBlankElements = editor.dom.select('.blank-highlight');

      // Check each blank element in the editor
      // Use for...of loop instead of forEach to avoid type issues
      for (const element of Array.from(allBlankElements)) {
        const blankId = element.getAttribute('data-blank-id');
        // If this blank ID is not in the blanks array, remove it from the editor
        if (blankId && !blanks.some((blank) => blank.id === blankId)) {
          editor.dom.remove(element);
        }
      }
    }
  }, [blanks]);

  const handleCreateQuestion = async () => {
    const answers = blanks.map((blank) => blank.answer);
    try {
      // Ensure we have the latest raw text with [[gap]] markers
      let currentRawText = rawText;
      if (editorRef.current) {
        const plainText = editorRef.current.getContent({ format: 'text' });
        // Replace "___×" with "[[gap]]" and remove any extra newlines
        currentRawText = plainText.replace(/___[\s\n]*×[\s\n]*/g, '[[gap]]');
      }

      const response = await api.post(
        `/play/waterfall/admin/sets/${id}/question`,

        {
          question: {
            question_text: editorContent,
            question_text_plain: currentRawText,
            correct_answers: answers,
            options: Object.values(blocks).concat(Object.values(answers)),
          },
          // points: points,
        }
      );
      console.log(response);
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <motion.div
      className=""
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
    >
      <div>
        <h1 className="text-xl font-semibold text-gray-900 mb-4">
          Create Question
        </h1>
      </div>

      <div className="bg-white rounded-lg shadow-lg border p-5 relative">
        <div className="bg-gray-100 p-5 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium text-gray-800">
              Write Question And Add Blank
            </h2>
          </div>

          <div className="mb-4 relative">
            <Editor
              apiKey={
                process.env.NEXT_TINYMCE_TOKEN ||
                'w64sqy7kf7wf2qoy3qqfmyatf85cys00il0jcezjers4pl9o'
              }
              onInit={(_, editor) => (editorRef.current = editor)}
              init={{
                height: 200,
                menubar: false,
                plugins: [' autoresize'],
                toolbar: 'bold italic underline | formatselect | link',
                content_style: `
                body { font-family: Inter, -apple-system, BlinkMacSystemFont, sans-serif; font-size: 14px; }
                .blank-highlight {
                  background-color: #FFF8E0;
                  padding: 2px 6px;
                  border-radius: 4px;
                  margin: 0 3px;
                  display: inline-flex;
                  align-items: center;
                  border: 1px dashed #F59E0B;
                  cursor: default;
                  user-select: none;
                  font-weight: bold;
                  letter-spacing: 2px;
                }
                .blank-highlight:hover {
                  background-color: #FFE4A0;
                }
                .blank-remove {
                  margin-left: 4px;
                  cursor: pointer;
                  color: #EF4444;
                  font-weight: bold;
                  background-color: rgba(239, 68, 68, 0.1);
                  border-radius: 50%;
                  width: 16px;
                  height: 16px;
                  display: inline-flex;
                  align-items: center;
                  justify-content: center;
                }
                .blank-remove:hover {
                  background-color: rgba(239, 68, 68, 0.2);
                }
              `,
                setup: (editor) => {
                  // Store a reference to the removeBlank function
                  const handleRemoveBlank = (id) => {
                    console.log('Removing blank with ID:', id);
                    dispatch(removeBlank(id));

                    // Update raw text after removing a blank
                    setTimeout(() => {
                      const plainText = editor.getContent({ format: 'text' });
                      // Replace "___×" with "[[gap]]" and remove any extra newlines
                      const processedText = plainText.replace(
                        /___[\s\n]*×[\s\n]*/g,
                        '[[gap]]'
                      );
                      setRawText(processedText);
                    }, 0);
                  };

                  // Handle clicks on the remove button
                  editor.on('click', (e) => {
                    const target = e.target;
                    if (target.className === 'blank-remove') {
                      const blankElement = target.closest('.blank-highlight');
                      if (blankElement) {
                        const blankId =
                          blankElement.getAttribute('data-blank-id');
                        if (blankId) {
                          // Remove the blank element from the editor
                          editor.dom.remove(blankElement);
                          // Remove the blank from the state to update the answer section
                          handleRemoveBlank(blankId);
                        }
                      }
                    }
                  });

                  // Prevent editing of blank elements
                  editor.on('BeforeSetContent', (e) => {
                    // This event fires before content is set in the editor
                    if (
                      e.content &&
                      e.content.indexOf('blank-highlight') !== -1
                    ) {
                      // If the content contains a blank, make sure it's not editable
                      e.content = e.content.replace(
                        /<span class="blank-highlight"/g,
                        '<span class="blank-highlight" contenteditable="false"'
                      );
                    }
                  });

                  // Prevent selection inside blank elements
                  editor.on('NodeChange', (e) => {
                    const node = e.element;
                    if (
                      node &&
                      (node.className === 'blank-highlight' ||
                        (node.parentNode &&
                          node.parentNode.className === 'blank-highlight'))
                    ) {
                      // If the cursor is inside a blank element, move it outside
                      const blankElement =
                        node.className === 'blank-highlight'
                          ? node
                          : node.parentNode;

                      // Place cursor after the blank element
                      const range = editor.selection.getRng();
                      range.setStartAfter(blankElement);
                      range.setEndAfter(blankElement);
                      editor.selection.setRng(range);
                    }
                  });
                },
              }}
              onEditorChange={handleEditorChange}
            />

            <div className="absolute w-full h-[27px] rounded-b-lg bottom-0 bg-white border-[2px] border-t-0 z-10"></div>

            <div className="flex justify-end absolute right-2 top-1.5 z-10">
              <button
                onClick={handleAddBlank}
                className="flex items-center gap-2 bg-[#FFDE34] text-black text-base rounded px-6 py-1.5 hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
              >
                Add Blank
              </button>
            </div>
          </div>

          {blanks.length > 0 && <BlankAnswers />}

          <div className="mt-4">
            <WordBlockInput
              label="Blocks"
              required
              value={blocks}
              onChange={handleBlocksChange}
            />
          </div>
        </div>

        {/* <div className="flex justify-center space-x-4 mt-8">
          <button className="px-4 py-2 bg-yellow-300 hover:bg-yellow-400 rounded">
            <Icon
              icon="icons8:plus"
              width="20"
              height="20"
              className="inline-block mb-1 mr-1"
            />
            Add More Question
          </button>
        </div> */}
      </div>

      <div className="flex justify-end space-x-4 mt-8">
        <button className="bg-gray-300 hover:bg-gray-50 text-black font-medium py-2 px-4 rounded">
          Cancel
        </button>
        <button
          onClick={handleCreateQuestion}
          className="bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded"
        >
          Create
        </button>
      </div>
    </motion.div>
  );
};

export default QuestionCreatorPage;
