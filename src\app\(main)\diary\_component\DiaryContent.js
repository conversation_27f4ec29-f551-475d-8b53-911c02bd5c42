import React from 'react';
import DiaryPage from './DiaryPage';
import DiaryIconsSidebar from './DiaryIconsSidebar';

const DiaryContent = ({ entries, currentIndex }) => {
  // Check if there's a next entry to display
  const hasNextEntry = currentIndex + 1 < entries.length;

  return (
    <div className="absolute inset-0 flex overflow-hidden">
      {/* Left page - always shown */}
            {/* <DiaryIconsSidebar position="right-0 top-1/4" /> */}

      <div className={`${hasNextEntry ? 'w-1/2' : 'w-full'} bg-white ${hasNextEntry ? 'border-r border-gray-300' : ''}`}>
        <DiaryPage entry={entries[currentIndex]} />
      </div>

      {/* Right page - only shown if there's a next entry */}
      {hasNextEntry && (
        <div className="w-1/2 bg-white">
          <DiaryPage entry={entries[currentIndex + 1]} />
        </div>
      )}
    </div>
  );
};

export default DiaryContent;
