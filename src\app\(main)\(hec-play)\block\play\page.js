'use client';
import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { Icon } from '@iconify/react';
import Image from 'next/image';
import api from '@/lib/api';

const BlockPlay = () => {
  const [gameData, setGameData] = useState(null);
  const [selectedWords, setSelectedWords] = useState({});
  const [completedSentences, setCompletedSentences] = useState({});
  const [score, setScore] = useState(0);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [draggedWord, setDraggedWord] = useState(null);
  const [currentSentenceIndex, setCurrentSentenceIndex] = useState(0);

  // Fetch game data
  const { data: gameResponse, isLoading, error, refetch } = useQuery({
    queryKey: ['block-play-game'],
    queryFn: async () => {
      console.log('Fetching block play game...');
      try {
        const response = await api.get('/play/block/play');
        console.log('API Response:', response);
        return response.data;
      } catch (err) {
        console.error('API Error:', err);
        throw err;
      }
    }
  });

  // Debug logs
  console.log('Component State:', {
    isLoading,
    error,
    gameResponse,
    gameData
  });

  // Initialize game data when response is received
  useEffect(() => {
    console.log('useEffect triggered with gameResponse:', gameResponse);

    // The API response structure is: { data: { id, title, score, ... } }
    // But our API call already extracts response.data, so gameResponse IS the game data
    if (gameResponse) {
      console.log('Setting gameData to:', gameResponse);
      setGameData(gameResponse);

      // Initialize selected words structure
      const initialSelected = {};
      gameResponse.sentences.forEach(sentence => {
        initialSelected[sentence.sentence_order] = {
          starting_part: [],
          expanding_part: []
        };
      });
      setSelectedWords(initialSelected);
      console.log('Initialized selectedWords:', initialSelected);
    }
  }, [gameResponse]);

  // Handle drag start
  const handleDragStart = (e, word, wordType) => {
    setDraggedWord({ word, wordType });
    e.dataTransfer.effectAllowed = 'move';
  };

  // Handle drag over
  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  // Handle drop
  const handleDrop = (e, sentenceOrder, partType) => {
    e.preventDefault();

    if (!draggedWord) return;

    const { word, wordType } = draggedWord;

    // Check if word type matches the drop zone
    if ((partType === 'starting_part' && wordType !== 'starting_words') ||
        (partType === 'expanding_part' && wordType !== 'expanding_words')) {
      setDraggedWord(null);
      return;
    }

    // Add word to selected words
    setSelectedWords(prev => ({
      ...prev,
      [sentenceOrder]: {
        ...prev[sentenceOrder],
        [partType]: [...prev[sentenceOrder][partType], word]
      }
    }));

    setDraggedWord(null);
  };

  // Remove word from selected
  const removeWord = (sentenceOrder, partType, wordIndex) => {
    setSelectedWords(prev => ({
      ...prev,
      [sentenceOrder]: {
        ...prev[sentenceOrder],
        [partType]: prev[sentenceOrder][partType].filter((_, index) => index !== wordIndex)
      }
    }));
  };

  // Check if sentence is complete and correct
  const checkSentence = (sentenceOrder) => {
    const sentence = gameData.sentences.find(s => s.sentence_order === sentenceOrder);
    const selected = selectedWords[sentenceOrder];

    if (!sentence || !selected) return false;

    const startingCorrect = selected.starting_part.join(' ').toLowerCase() ===
                           sentence.starting_part.toLowerCase();
    const expandingCorrect = selected.expanding_part.join(' ').toLowerCase() ===
                            sentence.expanding_part.toLowerCase();

    return startingCorrect && expandingCorrect;
  };

  // Submit sentence for checking
  const submitSentence = (sentenceOrder) => {
    if (checkSentence(sentenceOrder)) {
      setCompletedSentences(prev => ({
        ...prev,
        [sentenceOrder]: true
      }));

      // Calculate score (equal points per sentence)
      const pointsPerSentence = gameData.score / gameData.sentence_count;
      setScore(prev => prev + pointsPerSentence);

      // Check if game is completed
      const totalCompleted = Object.keys(completedSentences).length + 1;
      if (totalCompleted === gameData.sentence_count) {
        setGameCompleted(true);
        // Auto-submit results when game is completed
        setTimeout(() => {
          submitGameResults();
        }, 1000); // Delay to ensure state is updated
      }
    } else {
      // Show feedback for incorrect answer
      alert('Incorrect! Please try again.');
    }
  };

  // Move to next sentence
  const goToNextSentence = () => {
    if (currentSentenceIndex < gameData.sentences.length - 1) {
      setCurrentSentenceIndex(prev => prev + 1);
    }
  };

  // Get current sentence
  const getCurrentSentence = () => {
    if (!gameData || !gameData.sentences) return null;
    return gameData.sentences[currentSentenceIndex];
  };

  // Get word count for starting part
  const getStartingWordCount = (sentence) => {
    if (!sentence) return 0;
    return sentence.starting_part.split(' ').length;
  };

  // Get word count for expanding part
  const getExpandingWordCount = (sentence) => {
    if (!sentence) return 0;
    return sentence.expanding_part.split(' ').length;
  };

  // Create blank blocks array
  const createBlankBlocks = (count, filledWords) => {
    const blocks = [];
    for (let i = 0; i < count; i++) {
      if (i < filledWords.length) {
        // Filled block
        blocks.push({
          type: 'filled',
          word: filledWords[i],
          index: i
        });
      } else {
        // Empty block
        blocks.push({
          type: 'empty',
          index: i
        });
      }
    }
    return blocks;
  };

  // Get available words (not used in completed sentences)
  const getAvailableWords = (wordType) => {
    if (!gameData) return [];

    const usedWords = new Set();

    // Add words from completed sentences
    Object.keys(completedSentences).forEach(sentenceOrder => {
      const sentence = gameData.sentences.find(s => s.sentence_order === parseInt(sentenceOrder));
      if (sentence) {
        if (wordType === 'starting_words') {
          sentence.starting_part.split(' ').forEach(word => usedWords.add(word));
        } else {
          sentence.expanding_part.split(' ').forEach(word => usedWords.add(word));
        }
      }
    });

    // Add words from current selections
    Object.values(selectedWords).forEach(selection => {
      if (wordType === 'starting_words') {
        selection.starting_part.forEach(word => usedWords.add(word));
      } else {
        selection.expanding_part.forEach(word => usedWords.add(word));
      }
    });

    return gameData.word_blocks[wordType].filter(word => !usedWords.has(word));
  };

  // Submit game results
  const submitGameResults = async () => {
    if (!gameData) return;

    // Prepare sentence constructions from completed sentences
    const sentenceConstructions = gameData.sentences
      .filter(sentence => completedSentences[sentence.sentence_order])
      .map(sentence => ({
        starting_sentence: selectedWords[sentence.sentence_order]?.starting_part.join(' ') || '',
        expanding_sentence: selectedWords[sentence.sentence_order]?.expanding_part.join(' ') || '',
        sentence_order: sentence.sentence_order
      }));

    const payload = {
      block_game_id: gameData.id,
      sentence_constructions: sentenceConstructions
    };

    try {
      const response = await api.post('/play/block/submit', payload);
      console.log('Game submitted successfully:', response.data);
      // You can add success feedback here
    } catch (error) {
      console.error('Error submitting game:', error);
      // You can add error feedback here
    }
  };

  // Reset game
  const resetGame = () => {
    refetch();
    setSelectedWords({});
    setCompletedSentences({});
    setScore(0);
    setGameCompleted(false);
    setCurrentSentenceIndex(0);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <Icon icon="lucide:loader-2" className="w-12 h-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 text-lg">Loading your game...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-lg p-8 shadow-lg">
          <Icon icon="lucide:alert-circle" className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <h2 className="text-xl font-bold text-gray-800 mb-2">Game Not Available</h2>
          <p className="text-gray-600 mb-4">Unable to load the game. Please try again.</p>
          <div className="space-y-2">
            <button
              onClick={resetGame}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors mr-2"
            >
              Try Again
            </button>
            <button
              onClick={() => {
                // Load demo data for testing
                const demoData = {
                  id: "demo-id",
                  title: "Demo Block Game",
                  score: 20,
                  sentence_count: 2,
                  sentences: [
                    {
                      starting_part: "The lights keep",
                      expanding_part: "flickering at night",
                      sentence_order: 1
                    },
                    {
                      starting_part: "We should call",
                      expanding_part: "an electrician soon",
                      sentence_order: 2
                    }
                  ],
                  word_blocks: {
                    starting_words: ["The", "should", "call", "We", "lights", "keep"],
                    expanding_words: ["soon", "an", "flickering", "night", "electrician", "at"]
                  }
                };
                setGameData(demoData);
                const initialSelected = {};
                demoData.sentences.forEach(sentence => {
                  initialSelected[sentence.sentence_order] = {
                    starting_part: [],
                    expanding_part: []
                  };
                });
                setSelectedWords(initialSelected);
              }}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors"
            >
              Load Demo Game
            </button>
          </div>
          <div className="mt-4 text-sm text-gray-500">
            Error: {error?.message || 'Unknown error'}
          </div>
        </div>
      </div>
    );
  }

  if (!gameData) return null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-yellow-50 to-orange-50 p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md border-2 border-yellow-300 p-6 mb-6">
          <div className="flex justify-between items-center">
            {/* Left side with image and title */}
            <div className="flex items-center space-x-4">
              <div className="flex-shrink-0">
                <Image
                  src="/assets/images/all-img/block.png"
                  alt="Block Play"
                  width={80}
                  height={80}
                  className="rounded-lg"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-orange-800">{gameData.title}</h1>
                <p className="text-orange-700">Build sentences by dragging words to the correct positions</p>
                <div className="mt-3">
                  <span className="text-sm font-medium text-orange-600">
                    Question {currentSentenceIndex + 1} of {gameData.sentence_count}
                  </span>
                  <div className="w-full bg-yellow-100 rounded-full h-3 mt-2 border border-yellow-300">
                    <div
                      className="bg-gradient-to-r from-yellow-400 to-orange-400 h-full rounded-full transition-all duration-500"
                      style={{ width: `${((currentSentenceIndex + 1) / gameData.sentence_count) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side with score */}
            <div className="text-right">
              <div className="text-2xl font-bold text-orange-600 mb-1">Score: {Math.round(score)}</div>
              <div className="text-sm text-orange-500">Max: {gameData.score} points</div>
            </div>
          </div>
        </div>

        {/* Game Completed Modal */}
        {gameCompleted && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 text-center max-w-md mx-4 shadow-lg border-2 border-yellow-300">
              <Icon icon="lucide:trophy" className="w-16 h-16 mx-auto mb-4 text-yellow-500" />
              <h2 className="text-2xl font-bold text-orange-800 mb-2">Congratulations!</h2>
              <p className="text-orange-700 mb-4">You completed all sentences!</p>
              <div className="text-3xl font-bold text-orange-600 mb-6">
                Final Score: {Math.round(score)}/{gameData.score}
              </div>
              <div className="space-y-3">
                <button
                  onClick={submitGameResults}
                  className="bg-gradient-to-r from-green-400 to-emerald-400 hover:from-green-500 hover:to-emerald-500 text-white px-6 py-3 rounded-lg transition-colors font-medium shadow-md w-full"
                >
                  Submit Results
                </button>
                <button
                  onClick={resetGame}
                  className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 text-white px-6 py-3 rounded-lg transition-colors font-medium shadow-md w-full"
                >
                  Play Again
                </button>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Word Blocks */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md border-2 border-yellow-300 p-6">
              <h2 className="text-lg font-semibold text-orange-800 mb-6">Word Blocks</h2>

              {/* Starting Words */}
              <div className="mb-6">
                <div className="border-b border-yellow-200 pb-2 mb-4">
                  <h3 className="text-md font-medium text-orange-700">Starting Words</h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {getAvailableWords('starting_words').map((word, index) => (
                    <div
                      key={`starting-${word}-${index}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, word, 'starting_words')}
                      className="bg-gradient-to-r from-yellow-100 to-orange-100 border border-yellow-300 text-orange-700 px-3 py-2 rounded-lg cursor-move hover:from-yellow-200 hover:to-orange-200 transition-colors duration-200 font-medium"
                    >
                      {word}
                    </div>
                  ))}
                </div>
              </div>

              {/* Expanding Words */}
              <div>
                <div className="border-b border-yellow-200 pb-2 mb-4">
                  <h3 className="text-md font-medium text-orange-700">Expanding Words</h3>
                </div>
                <div className="flex flex-wrap gap-2">
                  {getAvailableWords('expanding_words').map((word, index) => (
                    <div
                      key={`expanding-${word}-${index}`}
                      draggable
                      onDragStart={(e) => handleDragStart(e, word, 'expanding_words')}
                      className="bg-gradient-to-r from-amber-100 to-yellow-100 border border-amber-300 text-amber-700 px-3 py-2 rounded-lg cursor-move hover:from-amber-200 hover:to-yellow-200 transition-colors duration-200 font-medium"
                    >
                      {word}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Sentence Building Area */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md border-2 border-yellow-300">
              {/* Tab Navigation */}
              <div className="border-b border-yellow-200">
                <nav className="flex space-x-1 p-2">
                  {gameData.sentences.map((sentence, index) => (
                    <button
                      key={sentence.sentence_order}
                      onClick={() => setCurrentSentenceIndex(index)}
                      className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center space-x-2 ${
                        currentSentenceIndex === index
                          ? 'bg-gradient-to-r from-yellow-400 to-orange-400 text-white shadow-md'
                          : 'text-orange-700 hover:bg-yellow-100'
                      }`}
                    >
                      <span>Sentence {sentence.sentence_order}</span>
                      {completedSentences[sentence.sentence_order] && (
                        <Icon icon="lucide:check-circle" className="w-4 h-4 text-green-500" />
                      )}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {(() => {
                  const currentSentence = getCurrentSentence();
                  if (!currentSentence) return null;

                  return (
                    <div>
                      <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-semibold text-orange-800">
                          Sentence {currentSentence.sentence_order}
                        </h3>
                        {completedSentences[currentSentence.sentence_order] && (
                          <div className="flex items-center text-orange-600 bg-white px-3 py-1 rounded-full border border-orange-300">
                            <Icon icon="lucide:check-circle" className="w-4 h-4 mr-1" />
                            <span className="text-sm font-medium">Completed!</span>
                          </div>
                        )}
                      </div>

                      {!completedSentences[currentSentence.sentence_order] ? (
                        <div className="space-y-4">
                          {/* Starting Part Drop Zone */}
                          <div>
                            <div className="border-b border-yellow-200 pb-2 mb-3">
                              <label className="block text-md font-medium text-orange-700">Starting Part</label>
                            </div>
                            <div
                              onDragOver={handleDragOver}
                              onDrop={(e) => handleDrop(e, currentSentence.sentence_order, 'starting_part')}
                              className="min-h-[60px] border-2 border-dashed border-yellow-300 rounded-lg p-3 flex flex-wrap gap-2 items-center"
                            >
                              {createBlankBlocks(
                                getStartingWordCount(currentSentence),
                                selectedWords[currentSentence.sentence_order]?.starting_part || []
                              ).map((block, blockIndex) => (
                                <div key={blockIndex} className="relative">
                                  {block.type === 'filled' ? (
                                    <span
                                      onClick={() => removeWord(currentSentence.sentence_order, 'starting_part', block.index)}
                                      className="bg-white text-gray-700 px-3 py-2 rounded-md cursor-pointer hover:bg-gray-50 transition-colors duration-200 font-medium border border-gray-300 shadow-sm"
                                    >
                                      {block.word} ×
                                    </span>
                                  ) : (
                                    <div className="w-16 h-8 border-2 border-dashed border-gray-300 rounded-md bg-white flex items-center justify-center">
                                      <span className="text-gray-400 text-xs">_</span>
                                    </div>
                                  )}
                                </div>
                              ))}
                              {getStartingWordCount(currentSentence) === 0 && (
                                <span className="text-orange-600 text-sm italic">
                                  Drag starting words here...
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Expanding Part Drop Zone */}
                          <div>
                            <div className="border-b border-yellow-200 pb-2 mb-3">
                              <label className="block text-md font-medium text-orange-700">Expanding Part</label>
                            </div>
                            <div
                              onDragOver={handleDragOver}
                              onDrop={(e) => handleDrop(e, currentSentence.sentence_order, 'expanding_part')}
                              className="min-h-[60px] border-2 border-dashed border-amber-300 rounded-lg p-3 flex flex-wrap gap-2 items-center"
                            >
                              {createBlankBlocks(
                                getExpandingWordCount(currentSentence),
                                selectedWords[currentSentence.sentence_order]?.expanding_part || []
                              ).map((block, blockIndex) => (
                                <div key={blockIndex} className="relative">
                                  {block.type === 'filled' ? (
                                    <span
                                      onClick={() => removeWord(currentSentence.sentence_order, 'expanding_part', block.index)}
                                      className="bg-white text-gray-700 px-3 py-2 rounded-md cursor-pointer hover:bg-gray-50 transition-colors duration-200 font-medium border border-gray-300 shadow-sm"
                                    >
                                      {block.word} ×
                                    </span>
                                  ) : (
                                    <div className="w-16 h-8 border-2 border-dashed border-gray-300 rounded-md bg-white flex items-center justify-center">
                                      <span className="text-gray-400 text-xs">_</span>
                                    </div>
                                  )}
                                </div>
                              ))}
                              {getExpandingWordCount(currentSentence) === 0 && (
                                <span className="text-amber-600 text-sm italic">
                                  Drag expanding words here...
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Complete Sentence Preview */}
                          <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                            <h4 className="text-sm font-medium text-orange-700 mb-2">Your Sentence:</h4>
                            <p className="text-lg text-orange-800 font-medium">
                              {selectedWords[currentSentence.sentence_order]?.starting_part.join(' ')}
                              {selectedWords[currentSentence.sentence_order]?.starting_part.length > 0 &&
                               selectedWords[currentSentence.sentence_order]?.expanding_part.length > 0 && ' '}
                              {selectedWords[currentSentence.sentence_order]?.expanding_part.join(' ')}
                              {selectedWords[currentSentence.sentence_order]?.starting_part.length === 0 &&
                               selectedWords[currentSentence.sentence_order]?.expanding_part.length === 0 && (
                                <span className="text-orange-400 italic">Build your sentence above...</span>
                              )}
                            </p>
                          </div>

                          {/* Submit and Navigation Buttons */}
                          <div className="flex justify-between items-center">
                            {/* Previous Button */}
                            <button
                              onClick={() => setCurrentSentenceIndex(prev => Math.max(0, prev - 1))}
                              disabled={currentSentenceIndex === 0}
                              className="bg-gray-400 hover:bg-gray-500 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors font-medium"
                            >
                              Previous
                            </button>

                            {/* Submit Button */}
                            <button
                              onClick={() => submitSentence(currentSentence.sentence_order)}
                              disabled={
                                !selectedWords[currentSentence.sentence_order]?.starting_part.length ||
                                !selectedWords[currentSentence.sentence_order]?.expanding_part.length
                              }
                              className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white px-6 py-2 rounded-lg transition-colors font-medium shadow-md"
                            >
                              Check Sentence
                            </button>

                            {/* Next Button */}
                            <button
                              onClick={() => setCurrentSentenceIndex(prev => Math.min(gameData.sentences.length - 1, prev + 1))}
                              disabled={currentSentenceIndex === gameData.sentences.length - 1}
                              className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors font-medium"
                            >
                              Next
                            </button>
                          </div>
                        </div>
                      ) : (
                        // Completed sentence display
                        <div className="space-y-4">
                          <div className="bg-yellow-100 rounded-lg p-4 border border-yellow-300">
                            <h4 className="text-sm font-medium text-orange-700 mb-2">Correct Sentence:</h4>
                            <p className="text-lg text-orange-800 font-medium">
                              {currentSentence.starting_part} {currentSentence.expanding_part}
                            </p>
                            <div className="mt-2 text-sm text-orange-600">
                              +{Math.round(gameData.score / gameData.sentence_count)} points
                            </div>
                          </div>

                          {/* Navigation Buttons for Completed */}
                          <div className="flex justify-between items-center">
                            {/* Previous Button */}
                            <button
                              onClick={() => setCurrentSentenceIndex(prev => Math.max(0, prev - 1))}
                              disabled={currentSentenceIndex === 0}
                              className="bg-gray-400 hover:bg-gray-500 disabled:bg-gray-300 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors font-medium"
                            >
                              Previous
                            </button>

                            {/* Next Button */}
                            <button
                              onClick={() => setCurrentSentenceIndex(prev => Math.min(gameData.sentences.length - 1, prev + 1))}
                              disabled={currentSentenceIndex === gameData.sentences.length - 1}
                              className="bg-gradient-to-r from-yellow-400 to-orange-400 hover:from-yellow-500 hover:to-orange-500 disabled:from-gray-300 disabled:to-gray-400 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg transition-colors font-medium"
                            >
                              Next
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                })()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlockPlay;
