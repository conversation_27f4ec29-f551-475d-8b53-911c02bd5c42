'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  selectMessages,
  selectMe,
  selectRemoteTyping,
  selectIsLoadingMessages,
} from '@/store/features/chatSlice';
import ImagePreviewModal from '@/components/shared/ImagePreviewModal';

const MessageList = () => {
  const messages = useSelector(selectMessages);
  const me = useSelector(selectMe);
  const remoteTyping = useSelector(selectRemoteTyping);
  const isLoading = useSelector(selectIsLoadingMessages);
  const messagesEndRef = useRef(null);
  const messagesContainerRef = useRef(null);

  // Image preview modal state
  const [imagePreview, setImagePreview] = useState({
    isOpen: false,
    imageUrl: '',
    imageName: '',
    imageAlt: ''
  });

  const handleImageClick = (attachment) => {
    setImagePreview({
      isOpen: true,
      imageUrl: attachment.fileUrl || attachment.url,
      imageName: attachment.fileName || attachment.name || 'Image',
      imageAlt: attachment.fileName || attachment.name || 'Chat image'
    });
  };

  const closeImagePreview = () => {
    setImagePreview({
      isOpen: false,
      imageUrl: '',
      imageName: '',
      imageAlt: ''
    });
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    const scrollToBottom = () => {
      if (messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    };

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [messages, remoteTyping]);

  const statusIcon = (status) => {
    switch (status) {
      case 'sending':
        return '…';
      case 'sent':
        return '✓';
      case 'delivered':
        return '✓✓';
      case 'read':
        return '✓✓';
      default:
        return '';
    }
  };

  const isImage = (attachment) => {
    const mimeType = attachment.mimeType || attachment.type || '';
    const url = attachment.url || attachment.fileUrl || '';
    return mimeType.startsWith('image') || /\.(jpe?g|png|webp|gif|bmp)$/i.test(url);
  };

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  if (isLoading) {
    return (
      <div
        style={{
          flex: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          background: '#f7f9fb',
        }}
      >
        <div style={{ color: '#6b7280', fontSize: '14px' }}>Loading messages...</div>
      </div>
    );
  }

  return (
    <div
      ref={messagesContainerRef}
      style={{
        flex: 1,
        overflowY: 'auto',
        padding: '20px',
        background: '#f7f9fb',
      }}
    >
      {messages.length === 0 ? (
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#6b7280',
            fontSize: '14px',
          }}
        >
          No messages yet. Start the conversation!
        </div>
      ) : (
        <>
          {messages.map((message) => {
            const isOwn = message.sender.id === me?.id;
            return (
              <div
                key={message.id}
                style={{
                  display: 'flex',
                  justifyContent: isOwn ? 'flex-end' : 'flex-start',
                  marginBottom: '12px',
                }}
              >
                <div
                  style={{
                    maxWidth: '70%',
                    padding: '10px 14px',
                    fontSize: '14px',
                    background: isOwn ? '#2563eb' : '#e5e7eb',
                    color: isOwn ? '#fff' : '#000',
                    borderRadius: '16px',
                    borderBottomRightRadius: isOwn ? '4px' : '16px',
                    borderBottomLeftRadius: isOwn ? '16px' : '4px',
                    position: 'relative',
                    wordWrap: 'break-word',
                  }}
                >
                  {message.content && (
                    <div style={{ marginBottom: message.attachments?.length > 0 ? '8px' : '0' }}>
                      {message.content}
                    </div>
                  )}

                  {message.attachments?.length > 0 && (
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        gap: '6px',
                      }}
                    >
                      {message.attachments.map((attachment) =>
                        isImage(attachment) ? (
                          <img
                            key={attachment.id}
                            src={attachment.fileUrl || attachment.url}
                            alt={attachment.fileName || attachment.name || 'Image'}
                            onClick={() => handleImageClick(attachment)}
                            style={{
                              maxWidth: '200px',
                              maxHeight: '200px',
                              borderRadius: '8px',
                              objectFit: 'cover',
                              cursor: 'pointer',
                              transition: 'opacity 0.2s ease',
                            }}
                            onMouseEnter={(e) => e.target.style.opacity = '0.8'}
                            onMouseLeave={(e) => e.target.style.opacity = '1'}
                          />
                        ) : (
                          <a
                            key={attachment.id}
                            href={attachment.fileUrl || attachment.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            style={{
                              fontSize: '12px',
                              color: isOwn ? '#bfdbfe' : '#2563eb',
                              textDecoration: 'underline',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '4px',
                            }}
                          >
                            <span>📎</span>
                            {attachment.fileName || attachment.name || 'Download'}
                          </a>
                        )
                      )}
                    </div>
                  )}

                  <div
                    style={{
                      fontSize: '11px',
                      color: isOwn ? 'rgba(255,255,255,0.7)' : '#9ca3af',
                      marginTop: '4px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                    }}
                  >
                    <span>{formatTime(message.createdAt)}</span>
                    {isOwn && (
                      <span
                        style={{
                          marginLeft: '8px',
                          color:
                            message.status === 'read'
                              ? '#60a5fa'
                              : message.status === 'delivered'
                              ? 'rgba(255,255,255,0.7)'
                              : '#9ca3af',
                        }}
                      >
                        {statusIcon(message.status)}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}

          {remoteTyping && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-start',
                marginBottom: '12px',
              }}
            >
              <div
                style={{
                  padding: '10px 14px',
                  fontSize: '13px',
                  background: '#e5e7eb',
                  color: '#6b7280',
                  borderRadius: '16px',
                  borderBottomLeftRadius: '4px',
                  fontStyle: 'italic',
                }}
              >
                typing...
              </div>
            </div>
          )}

          <div ref={messagesEndRef} />
        </>
      )}

      {/* Image Preview Modal */}
      <ImagePreviewModal
        isOpen={imagePreview.isOpen}
        onClose={closeImagePreview}
        imageUrl={imagePreview.imageUrl}
        imageName={imagePreview.imageName}
        imageAlt={imagePreview.imageAlt}
      />
    </div>
  );
};

export default MessageList;
