'use client';
import React from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import api from '@/lib/api';
import FormInput from '@/components/form/FormInput';

const AddUser = () => {
  const queryClient = useQueryClient();

  // Create mutation for adding a student
  const createStudentMutation = useMutation({
    mutationFn: async (userData) => {
      try {
        const response = await api.post('/users/admin/create-student', userData);
        return response;
      } catch (error) {
        console.log('API Error caught:', error);
        console.log('Error response data:', error?.response?.data);
        
        // Extract error message from the response
        const errorMessage = error?.response?.data?.message || 
                            error?.message || 
                            'Failed to create student';
        
        // Show toast immediately
        toast.error(errorMessage);
        
        // Re-throw the error so React Query can handle it
        throw error;
      }
    },
    onSuccess: (data) => {
      // Invalidate and refetch relevant queries after successful mutation
      queryClient.invalidateQueries(['students']);
      // Show success toast
      toast.success('Student created successfully!');
    },
    onError: (error) => {
      console.log('React Query onError triggered:', error);
      // This is a fallback in case the try-catch doesn't work
      const errorMessage = error?.response?.data?.message || 
                          error?.message || 
                          'Failed to create student';
      toast.error(errorMessage);
    },
    onSettled: () => {
      // Actions to perform regardless of success or failure
    }
  });

  const validationSchema = Yup.object().shape({
    userId: Yup.string().required('ID is required'),
    email: Yup.string().email('Invalid email').required('Email is required'),
  });

  const initialValues = {
    userId: '',
    email: '',
  };

  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Only send the fields that the API expects
    const payload = {
      userId: values.userId,
      email: values.email
    };
    
    // Submit the form data to the API
    createStudentMutation.mutate(payload, {
      onSettled: () => {
        setSubmitting(false);
      },
      onSuccess: () => {
        resetForm();
      },
      onError: (error) => {
        console.log('Mutation error:', error);
        // This will also trigger the main onError handler above
      }
    });
  };

  return (
    <div className="min-h-screen bg-white p-5">
      <div className="p-4 border-b border-gray-200">
        <h2 className="card-title text-black text-xl">Add Student</h2>
        {createStudentMutation.isError && (
          <div className="mt-2 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            Error: {createStudentMutation.error?.response?.data?.message || 
                   createStudentMutation.error?.message || 
                   'Failed to create student'}
          </div>
        )}
        {createStudentMutation.isSuccess && (
          <div className="mt-2 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            Student created successfully!
          </div>
        )}
      </div>
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting, errors, touched }) => {
          return (
            <Form className="mx-auto bg-gray-100 p-6 rounded-lg shadow-md">
              <div className="flex flex-wrap gap-4 mb-4">
                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="userId" className="block font-bold">
                    ID <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="text"
                    name="userId"
                    id="userId"
                    placeholder="Write ID here"
                    className={`w-full mt-1 p-2 border ${
                      errors.userId && touched.userId ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                </div>

                <div className="flex-1 min-w-[200px]">
                  <label htmlFor="email" className="block font-bold">
                    Email Address <span className="text-red-500">*</span>
                  </label>
                  <FormInput
                    type="email"
                    name="email"
                    id="email"
                    placeholder="Enter email address"
                    className={`w-full mt-1 p-2 border ${
                      errors.email && touched.email ? 'border-red-500' : 'border-gray-300'
                    } rounded`}
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isSubmitting || createStudentMutation.isLoading}
                className={`bg-[#FFDE34] hover:bg-yellow-300 text-black font-medium py-2 px-4 rounded mt-10 absolute right-10 ${
                  (isSubmitting || createStudentMutation.isLoading) ? 'opacity-50 cursor-not-allowed' : ''
                }`}
              >
                {(isSubmitting || createStudentMutation.isLoading) ? 'Adding...' : 'Add Student'}
              </button>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default AddUser;