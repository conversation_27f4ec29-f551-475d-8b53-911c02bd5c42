import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // User and contacts
  me: null,
  contacts: [],
  filteredContacts: [],
  searchTerm: '',

  // Active conversation
  activeContact: null,
  conversationId: null,
  messages: [],

  // UI state
  isTyping: false,
  remoteTyping: false,

  // Message input
  messageInput: '',
  attachedFiles: [],

  // Loading states
  isLoadingContacts: false,
  isLoadingMessages: false,
  isSendingMessage: false,

  // Socket connection
  isConnected: false,

  // Unread message tracking
  unreadCounts: {}, // { conversationId: count }
  totalUnreadCount: 0,
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    // User and contacts
    setMe: (state, action) => {
      state.me = action.payload;
    },
    setContacts: (state, action) => {
      state.contacts = action.payload;
      state.filteredContacts = action.payload;
    },
    updateContact: (state, action) => {
      const { conversationId, lastMessage, lastMessageTime, unreadCount } = action.payload;
      state.contacts = state.contacts.map(contact =>
        contact.conversationId === conversationId
          ? { ...contact, lastMessage, lastMessageTime, unreadCount: unreadCount ?? contact.unreadCount }
          : contact
      );
      // Update filtered contacts as well
      state.filteredContacts = state.filteredContacts.map(contact =>
        contact.conversationId === conversationId
          ? { ...contact, lastMessage, lastMessageTime, unreadCount: unreadCount ?? contact.unreadCount }
          : contact
      );
    },
    setSearchTerm: (state, action) => {
      state.searchTerm = action.payload;
      // Filter contacts based on search term
      if (action.payload.trim() === '') {
        state.filteredContacts = state.contacts;
      } else {
        const searchLower = action.payload.toLowerCase();
        state.filteredContacts = state.contacts.filter(contact =>
          contact.name.toLowerCase().includes(searchLower) ||
          (contact.lastMessage && contact.lastMessage.toLowerCase().includes(searchLower))
        );
      }
    },

    // Active conversation
    setActiveContact: (state, action) => {
      state.activeContact = action.payload;
      state.conversationId = action.payload?.conversationId || null;
    },
    setConversationId: (state, action) => {
      state.conversationId = action.payload;
    },
    clearActiveConversation: (state) => {
      state.activeContact = null;
      state.conversationId = null;
      state.messages = [];
      state.messageInput = '';
      state.attachedFiles = [];
    },

    // Messages
    setMessages: (state, action) => {
      // Ensure all messages have valid createdAt timestamps
      state.messages = action.payload.map(message => ({
        ...message,
        createdAt: message.createdAt && typeof message.createdAt === 'string'
          ? message.createdAt
          : new Date().toISOString()
      }));
    },
    addMessage: (state, action) => {
      const newMessage = action.payload;
      // Ensure createdAt is properly formatted
      if (!newMessage.createdAt || typeof newMessage.createdAt !== 'string') {
        newMessage.createdAt = new Date().toISOString();
      }
      // Check if message already exists
      const exists = state.messages.some(msg => msg.id === newMessage.id);
      if (!exists) {
        state.messages.push(newMessage);
        state.messages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      }
    },
    updateMessage: (state, action) => {
      const { id, updates } = action.payload;
      state.messages = state.messages.map(msg =>
        msg.id === id ? { ...msg, ...updates } : msg
      );
    },
    replaceOptimisticMessage: (state, action) => {
      const { localId, serverMessage } = action.payload;
      const index = state.messages.findIndex(msg => msg.id === localId);
      if (index !== -1) {
        state.messages[index] = serverMessage;
        state.messages.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      }
    },

    // Typing indicators
    setIsTyping: (state, action) => {
      state.isTyping = action.payload;
    },
    setRemoteTyping: (state, action) => {
      state.remoteTyping = action.payload;
    },

    // Message input
    setMessageInput: (state, action) => {
      state.messageInput = action.payload;
    },
    setAttachedFiles: (state, action) => {
      // Ensure only serializable file metadata is stored
      state.attachedFiles = action.payload.map(file => ({
        id: file.id,
        name: file.name,
        size: file.size,
        type: file.type,
        url: file.url
      }));
    },
    addAttachedFile: (state, action) => {
      // Extract only serializable file metadata
      const { id, name, size, type, url, preview } = action.payload;
      state.attachedFiles.push({ id, name, size, type, url, preview });
    },
    removeAttachedFile: (state, action) => {
      const index = action.payload;
      state.attachedFiles.splice(index, 1);
    },
    clearMessageInput: (state) => {
      state.messageInput = '';
      state.attachedFiles = [];
    },

    // Loading states
    setIsLoadingContacts: (state, action) => {
      state.isLoadingContacts = action.payload;
    },
    setIsLoadingMessages: (state, action) => {
      state.isLoadingMessages = action.payload;
    },
    setIsSendingMessage: (state, action) => {
      state.isSendingMessage = action.payload;
    },

    // Socket connection
    setIsConnected: (state, action) => {
      state.isConnected = action.payload;
    },

    // Message status updates
    markMessagesAsDelivered: (state, action) => {
      const { conversationId, userId } = action.payload;
      if (state.conversationId === conversationId) {
        state.messages = state.messages.map(msg =>
          msg.sender.id === userId && msg.status === 'sent'
            ? { ...msg, status: 'delivered' }
            : msg
        );
      }
    },
    markMessagesAsRead: (state, action) => {
      const { conversationId, userId } = action.payload;
      if (state.conversationId === conversationId) {
        state.messages = state.messages.map(msg =>
          msg.sender.id === userId
            ? { ...msg, status: 'read' }
            : msg
        );
      }
    },

    // Unread message management
    incrementUnreadCount: (state, action) => {
      const { conversationId } = action.payload;
      // Only increment if not currently viewing this conversation
      if (conversationId !== state.conversationId) {
        state.unreadCounts[conversationId] = (state.unreadCounts[conversationId] || 0) + 1;
        state.totalUnreadCount += 1;

        // Update contact unread count
        state.contacts = state.contacts.map(contact =>
          contact.conversationId === conversationId
            ? { ...contact, unreadCount: state.unreadCounts[conversationId] }
            : contact
        );
        state.filteredContacts = state.filteredContacts.map(contact =>
          contact.conversationId === conversationId
            ? { ...contact, unreadCount: state.unreadCounts[conversationId] }
            : contact
        );
      }
    },
    clearUnreadCount: (state, action) => {
      const { conversationId } = action.payload;
      const previousCount = state.unreadCounts[conversationId] || 0;
      state.unreadCounts[conversationId] = 0;
      state.totalUnreadCount = Math.max(0, state.totalUnreadCount - previousCount);

      // Update contact unread count
      state.contacts = state.contacts.map(contact =>
        contact.conversationId === conversationId
          ? { ...contact, unreadCount: 0 }
          : contact
      );
      state.filteredContacts = state.filteredContacts.map(contact =>
        contact.conversationId === conversationId
          ? { ...contact, unreadCount: 0 }
          : contact
      );
    },
    setUnreadCounts: (state, action) => {
      state.unreadCounts = action.payload;
      state.totalUnreadCount = Object.values(action.payload).reduce((sum, count) => sum + count, 0);

      // Update contacts with unread counts
      state.contacts = state.contacts.map(contact => ({
        ...contact,
        unreadCount: state.unreadCounts[contact.conversationId] || 0
      }));
      state.filteredContacts = state.filteredContacts.map(contact => ({
        ...contact,
        unreadCount: state.unreadCounts[contact.conversationId] || 0
      }));
    },

    // Reset state
    resetChatState: () => initialState,
  },
});

export const {
  setMe,
  setContacts,
  updateContact,
  setSearchTerm,
  setActiveContact,
  setConversationId,
  clearActiveConversation,
  setMessages,
  addMessage,
  updateMessage,
  replaceOptimisticMessage,
  setIsTyping,
  setRemoteTyping,
  setMessageInput,
  setAttachedFiles,
  addAttachedFile,
  removeAttachedFile,
  clearMessageInput,
  setIsLoadingContacts,
  setIsLoadingMessages,
  setIsSendingMessage,
  setIsConnected,
  markMessagesAsDelivered,
  markMessagesAsRead,
  incrementUnreadCount,
  clearUnreadCount,
  setUnreadCounts,
  resetChatState,
} = chatSlice.actions;

// Selectors
export const selectMe = (state) => state.chat.me;
export const selectContacts = (state) => state.chat.contacts;
export const selectFilteredContacts = (state) => state.chat.filteredContacts;
export const selectSearchTerm = (state) => state.chat.searchTerm;
export const selectActiveContact = (state) => state.chat.activeContact;
export const selectConversationId = (state) => state.chat.conversationId;
export const selectMessages = (state) => state.chat.messages;
export const selectIsTyping = (state) => state.chat.isTyping;
export const selectRemoteTyping = (state) => state.chat.remoteTyping;
export const selectMessageInput = (state) => state.chat.messageInput;
export const selectAttachedFiles = (state) => state.chat.attachedFiles;
export const selectIsLoadingContacts = (state) => state.chat.isLoadingContacts;
export const selectIsLoadingMessages = (state) => state.chat.isLoadingMessages;
export const selectIsSendingMessage = (state) => state.chat.isSendingMessage;
export const selectIsConnected = (state) => state.chat.isConnected;
export const selectUnreadCounts = (state) => state.chat.unreadCounts;
export const selectTotalUnreadCount = (state) => state.chat.totalUnreadCount;

export default chatSlice.reducer;
