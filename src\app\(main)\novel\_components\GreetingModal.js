'use client';
import Modal from '@/components/Modal';
import React, { useState } from 'react';
import api from '@/lib/api';
import { Form, Formik } from 'formik';
import FormInput from '@/components/form/FormInput';
import Image from 'next/image';
import { ButtonIcon } from '@/components/Button';

const GreetingModal = ({ isOpen, onClose, title = 'Select Greeting' }) => {
  if (!isOpen) return null;

  const [applying, setApplying] = useState(false);

  const handleApply = async (values) => {
    try {
      setApplying(true);
      await api.post(`/student/novel/greeting`, values);
      onClose(); // Optionally close modal after apply
    } catch (err) {
      console.error('Failed to apply skin:', err);
    } finally {
      setApplying(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-xl overflow-hidden">
        <div className="relative">
          <div className="absolute z-10 top-2 right-3">
            <ButtonIcon
              icon="mdi:close"
              innerBtnCls="h-8 w-8"
              btnIconCls="h-5 w-5"
              onClick={onClose}
              aria-label="Close modal"
            />
          </div>
          <div className="bg-[#FFF6EF] p-6 relative shadow-lg text-center">
            <Image
              src={'/assets/images/all-img/woodFrame.png'}
              alt="Mission Confirmation"
              width={600}
              height={200}
              className="max-w-96 mx-auto h-auto"
              priority
            />

            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 mt-6 bg-[#FCF8EF] rounded-lg p-5 px-4 w-[55%]">
              <h2 className="text-2xl font-bold text-yellow-900 font-serif">
                {title}
              </h2>
            </div>
          </div>
        </div>

        <div className="p-4">
          <Formik initialValues={{ greeting: '' }} onSubmit={handleApply}>
            {({ values, handleChange, handleSubmit }) => (
              <Form>
                <FormInput
                  label="Greeting"
                  name="greeting"
                  type="text"
                  isTextarea={true}
                  required
                />

                {/* Apply Button */}
                <div className="flex justify-end">{console.log(applying)}
                  <button
                    onClick={handleApply}
                    disabled={applying}
                    className={`px-6 py-2 rounded-xl text-white font-medium transition-all border-2 border-white ${
                      applying
                        ? 'bg-gradient-to-b from-yellow-400 via-yellow-500 to-amber-600 hover:from-yellow-500 hover:to-amber-700'
                        : 'bg-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {applying ? 'Applying...' : 'Apply'}
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default GreetingModal;
