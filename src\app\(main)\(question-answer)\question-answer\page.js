'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import TinyMceEditor from '@/components/form/TinyMceEditor';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { toast } from 'sonner';

const HecAnswer = () => {
  const itemId = useSearchParams().get('itemId');
  const taskId = useSearchParams().get('taskId');
  const router = useRouter();
  const editorRef = useRef(null);
  const [value, setValue] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);
  const [responseData, setResponseData] = useState(null);

  const { data, refetch } = useDataFetch({
    queryKey: ['hec-answer', itemId],
    endPoint: `/student-qa-mission/activeTask`,
    enabled: !!itemId,
  });

  const { data: QnAData, refetch: QnARefetch } = useDataFetch({
    queryKey: ['hec-answer', taskId],
    endPoint: `student-qa-mission/task/${taskId}`,
    enabled: !!taskId,
  });

  useEffect(() => {
    if (data) {
      setResponseData(data);
    } else {
      setResponseData(QnAData);
    }
  }, [data, QnAData]);

  const showSubmission = responseData?.submissions?.length > 0 && !isSubmitted;
  const showFeedback =
    responseData?.submissions?.length > 0 &&
    responseData?.submissions[0]?.submissionMark;

  // Count words in HTML content
  const countWords = (html) => {
    if (!html) return 0;
    // Remove HTML tags
    const text = html?.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  const handleSubmit = async (values) => {
    console.log('values:', values);
    try {
      console.log('values:', values);
      // Check minimum word count
      const wordCount = countWords(values?.content?.answer);
      const minimumWords = responseData?.task?.wordLimitMinimum || 0;
      const maximumWords = responseData?.task?.wordLimitMaximum || Infinity;

      if (wordCount < minimumWords || wordCount > maximumWords) {
        toast.message(
          `Your answer must contain between ${minimumWords} and ${maximumWords} words. Current word count: ${wordCount}`
        );
        return;
      }

      const response = await api.post(`/student-qa-mission/submit/task`, {
        ...values,
        content: values?.content?.answer || values?.content,
        wordCount: wordCount,
      });
      console.log(response);
      router.push('/hec-mission');
      setIsSubmitted(true);
    } catch (error) {
      console.log(error);
    }
  };

  const handleUpdate = async (values) => {
    try {
      // Ensure we have the answer value
      if (!values || !values.content) return;

      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }

      const timeout = setTimeout(async () => {
        try {
          const submissionId =
            Array.isArray(responseData?.submissions) &&
            responseData.submissions.length > 0
              ? responseData.submissions[0].id
              : responseData?.id;

          const response = await api.post(
            `/student-qa-mission/submit/task/update`,
            {
              content: values.content,
              submissionId: submissionId,
              wordCount: wordCount,
            },
            { showSuccessToast: false }
          );
          console.log('Auto-saved (debounced):', response);
        } catch (error) {
          console.error('Auto-save error:', error);
        }
      }, 300);
      setAutoSaveTimeout(timeout);

      // Only refetch and update UI state on explicit submission, not auto-save
      if (!values._autoSave) {
        refetch();
        setIsSubmitted(false);
      }
    } catch (error) {
      console.error('Error updating submission:', error);
    }
  };

  // Handle component unmount - save draft
  useEffect(() => {
    return () => {
      setTimeout(() => {
        if (value && !isSubmitted && responseData?.id) {
          handleUpdate({
            content: value,
            _autoSave: true,
          });
        }
      }, 300);
    };
  }, [value, responseData?.id]);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Q & A'} linkClass="my-5 mb-8 w-full max-w-40" />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#EDFDFD] w-full rounded-lg shadow-lg space-y-5 relative">
            <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]  flex items-start justify-between">
              <div>
                <h1 className="text-2xl text-yellow-800 font-semibold">
                  {responseData?.task?.title || responseData?.title}
                </h1>
                <p>Instruction:</p>
                <EditorViewer
                  data={
                    responseData?.task?.instructions ||
                    responseData?.task?.description ||
                    responseData?.instructions
                  }
                />
              </div>

              <div className="relative">
                <Image
                  src={'/assets/images/all-img/boardFrame.png'}
                  alt={'block-play'}
                  width={500}
                  height={500}
                  className="max-w-52"
                />

                <ul className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-sm min-w-32">
                  <li>
                    Total Score:{' '}
                    {responseData?.task?.totalScore ||
                      responseData?.totalScore ||
                      0}
                  </li>
                  {/* <li>Time (Minutes): 20</li> */}
                </ul>
              </div>
            </div>
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Submission
                </h1>
                <EditorViewer
                  data={
                    (
                      responseData?.submissions
                        ?.map((item) => item?.submissionHistory?.content)
                        .join(' ') || ''
                    ).length > 200
                      ? responseData?.submissions
                          ?.map((item) => item?.submissionHistory?.content)
                          .join(' ')
                          .slice(0, 400) + '...'
                      : responseData?.submissions
                          ?.map((item) => item?.submissionHistory?.content)
                          .join(' ')
                  }
                />

                {responseData?.submissions[0]?.status !== 'submitted' && (
                  <div className="absolute right-2 top-2">
                    <ButtonIcon
                      icon={'ri:edit-2-fill'}
                      innerBtnCls={'h-10 w-10'}
                      btnIconCls={'h-5 w-5'}
                      onClick={() => setIsSubmitted(true)}
                    />
                  </div>
                )}
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {showFeedback && (
                  <>
                    {data?.submission?.corrections?.grammar?.length > 0 && (
                      <div className="rounded-md">
                        <ul className=" text-sm text-gray-800">
                          {data.submission.corrections.grammar.map(
                            (item, index) => (
                              <EditorViewer key={index} data={item} />
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setShowDetailsModal(true)}
                      />
                    </div>
                  </>
                )}

                <p
                  className={`${
                    !(data?.submission?.status === 'reviewed') && 'text-red-600'
                  } text-center mt-2`}
                >
                  {!(data?.submission?.status === 'reviewed') &&
                    'Not Confirmed yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                taskId: responseData?.task?.id,
                content:
                  value ||
                  responseData?.submissions
                    ?.map((item) => item?.submissionHistory?.content)
                    ?.join(' ') ||
                  responseData?.submissionHistory?.content ||
                  '',
              }}
              onSubmit={
                responseData?.submissions
                  ?.map((item) => item?.submissionHistory?.content)
                  ?.join(' ')
                  ? handleUpdate
                  : handleSubmit
              }
              enableReinitialize
            >
              {() => (
                <Form>
                  <TinyMceEditor
                    name="content"
                    editorRef={editorRef}
                    initialValue={
                      responseData?.submissions
                        ?.map((item) => item?.submissionHistory?.content)
                        ?.join(' ') || responseData?.submissionHistory?.content || ''
                    }
                    onAutoSave={(content) => {
                      setValue(content);
                      const newWordCount = countWords(
                        content?.answer || content
                      );
                      setWordCount(newWordCount);
                      // Set new timeout for auto-save
                      setTimeout(() => {
                        handleUpdate({
                          content: content,
                          _autoSave: true,
                        });
                      }, 300); // 0.3 second delay after typing stops
                    }}
                    setValue={setValue}
                    minWords={responseData?.task?.wordLimitMinimum}
                    maxWords={responseData?.task?.wordLimitMaximum || Infinity}
                  />

                  { <p
                    className={`text-right mt-2 ${
                      wordCount < responseData?.task?.wordLimitMinimum ||
                      wordCount > responseData?.task?.wordLimitMaximum
                        ? 'text-red-500'
                        : 'text-green-600'
                    }`}
                  >
                    {wordCount < responseData?.task?.wordLimitMinimum &&
                      `Minimum ${responseData?.task?.wordLimitMinimum} words required to submit.`}
                    {/* {(wordCount > data?.task?.wordLimitMaximum) &&
                      `Maximum ${data?.task?.wordLimitMaximum} words allowed.`} */}
                  </p>}

                  <div className="flex justify-center mt-14 gap-3">
                    <Button
                      buttonText="Cancel"
                      type="button"
                      onClick={() => setIsSubmitted(false)}
                    />
                    <Button
                      buttonText={
                        responseData?.submissions
                          ?.map((item) => item?.submissionHistory?.content)
                          ?.join(' ')
                          ? 'Update'
                          : 'Submit'
                      }
                      type="submit"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black"
                      // disabled={
                      //   wordCount < data?.task?.wordLimitMinimum ||
                      //   wordCount > data?.task?.wordLimitMaximum
                      // }
                    />
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <DetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={responseData?.submission?.corrections?.grammar}
        title="Teachers Feedback"
        link={`/answer`}
        showBtn={false}
      />
    </div>
  );
};

export default HecAnswer;
