'use client';

import React, { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useSearchParams } from 'next/navigation';
import Cookies from 'js-cookie';
import api from '@/lib/api';
import {
  setMe,
  setContacts,
  setActiveContact,
  setConversationId,
  setMessages,
  setIsLoadingContacts,
  setIsLoadingMessages,
  setIsSendingMessage,
  clearMessageInput,
  addMessage,
  replaceOptimisticMessage,
  updateContact,
  clearUnreadCount,
  selectActiveContact,
  selectConversationId,
  selectMe,
  selectMessageInput,
  selectAttachedFiles,
} from '@/store/features/chatSlice';
import { useChatSocket } from './hooks/useChatSocket';
import ContactList from './components/ContactList';
import ChatWindow from './components/ChatWindow';

const UPLOAD_ENDPOINT = '/chat/upload';
const token = Cookies.get('token') || '';

// Helper function to map server message format
const mapServerMessage = (serverMessage) => ({
  id: serverMessage.id,
  conversationId: serverMessage.conversationId,
  content: serverMessage.content,
  sender: { id: serverMessage.senderId },
  createdAt: serverMessage.createdAt || new Date().toISOString(),
  status: serverMessage.status || 'sent',
  attachments: serverMessage.attachments || [],
});

export default function ChatPage() {
  const dispatch = useDispatch();
  const searchParams = useSearchParams();

  // Redux selectors
  const activeContact = useSelector(selectActiveContact);
  const conversationId = useSelector(selectConversationId);
  const me = useSelector(selectMe);
  const messageInput = useSelector(selectMessageInput);
  const attachedFiles = useSelector(selectAttachedFiles);

  // Socket hook
  const { sendMessage, sendTyping } = useChatSocket();

  // Refs for socket callbacks
  const conversationIdRef = useRef(null);
  const meRef = useRef(null);

  // Update refs when values change
  useEffect(() => {
    conversationIdRef.current = conversationId;
  }, [conversationId]);

  useEffect(() => {
    meRef.current = me;
  }, [me]);

  // Initialize data on component mount
  useEffect(() => {
    if (!token) return;

    const initializeData = async () => {
      try {
        dispatch(setIsLoadingContacts(true));
        const contactsResponse = await api.get('/chat/contacts');
        dispatch(setContacts(contactsResponse.data || []));
      } catch (error) {
        console.error('Failed to load contacts:', error);
      } finally {
        dispatch(setIsLoadingContacts(false));
      }

      try {
        const userResponse = await api.get('/users/profile');
        dispatch(setMe(userResponse.data));
      } catch (error) {
        console.error('Failed to load user profile:', error);
      }
    };

    initializeData();
  }, [dispatch]);

  // Handle URL conversation ID parameter
  useEffect(() => {
    const urlConversationId = searchParams.get('conversationId');
    if (urlConversationId && urlConversationId !== conversationId) {
      // Find the contact with this conversation ID and set it as active
      // This will be handled by ContactList component when contacts are loaded
    }
  }, [searchParams, conversationId]);

  // Handle contact selection
  const handleContactSelect = async (contact) => {
    try {
      dispatch(setIsLoadingMessages(true));
      dispatch(setActiveContact(contact));
      dispatch(setConversationId(contact.conversationId));
      dispatch(setMessages([]));

      // Clear unread count for this conversation
      dispatch(clearUnreadCount({ conversationId: contact.conversationId }));

      // Load messages for the conversation
      const response = await api.get(`/chat/conversations/${contact.conversationId}/messages`);
      const messages = response.data?.items || response.messages || [];
      const mappedMessages = messages.map(mapServerMessage).sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
      dispatch(setMessages(mappedMessages));
    } catch (error) {
      console.error('Failed to load conversation:', error);
    } finally {
      dispatch(setIsLoadingMessages(false));
    }
  };

  // Handle typing indicator
  const handleTyping = (isTyping) => {
    if (sendTyping) {
      sendTyping(isTyping);
    }
  };

  // Handle file upload
  const uploadFiles = async (files) => {
    if (!files.length) return { attachments: [], ids: [] };

    const attachments = [];
    const ids = [];

    for (const fileData of files) {
      try {
        const formData = new FormData();
        formData.append('file', fileData.file);
        formData.append('conversationId', conversationId);

        const response = await api.post(UPLOAD_ENDPOINT, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });

        if (response.success && response.data) {
          // Extract only serializable file metadata
          const { id, name, size, type, url } = response.data;
          attachments.push({ id, name, size, type, url });
          ids.push(id);
        }
      } catch (error) {
        console.error('Failed to upload file:', fileData.file.name, error);
      }
    }

    return { attachments, ids };
  };

  // Handle sending message
  const handleSendMessage = async (filesWithObjects = null) => {
    const filesToUpload = filesWithObjects || attachedFiles;

    if ((!messageInput.trim() && filesToUpload.length === 0) || !conversationId || !me) {
      return;
    }

    try {
      dispatch(setIsSendingMessage(true));

      // Upload files first
      const { attachments, ids } = await uploadFiles(filesToUpload);

      // Create optimistic message
      const localId = `local-${Date.now()}-${Math.random()}`;
      const optimisticMessage = {
        id: localId,
        conversationId,
        content: messageInput.trim(),
        sender: me,
        createdAt: new Date(),
        status: 'sending',
        attachments,
      };

      // Add optimistic message to store
      dispatch(addMessage(optimisticMessage));

      // Update contact's last message
      dispatch(updateContact({
        conversationId,
        lastMessage: optimisticMessage.content,
        lastMessageTime: optimisticMessage.createdAt,
      }));

      // Clear input
      dispatch(clearMessageInput());

      // Prepare payload for socket
      const payload = {
        id: localId,
        conversationId,
        content: messageInput.trim(),
        senderId: me.id,
        recipientId: activeContact.id,
        type: 'text',
        timestamp: new Date().toISOString(),
        attachmentIds: ids,
      };

      // Send via socket
      if (sendMessage) {
        sendMessage(payload, (ack) => {
          if (ack) {
            const serverMessage = mapServerMessage(ack);
            dispatch(replaceOptimisticMessage({
              localId,
              serverMessage,
            }));
          }
        });
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      dispatch(setIsSendingMessage(false));
    }
  };

  return (
    <div
      style={{
        height: 'calc(100vh - 100px)',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'stretch',
        border: '1px solid #ddd',
        borderRadius: 8,
        overflow: 'hidden',
        fontFamily: 'Inter, sans-serif'
      }}
    >
      <ContactList onContactSelect={handleContactSelect} />
      <ChatWindow
        onSendMessage={handleSendMessage}
        onTyping={handleTyping}
      />
    </div>
  );
}
