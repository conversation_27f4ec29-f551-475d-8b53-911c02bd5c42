'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import api from '@/lib/api';
import useDataFetch from '@/hooks/useDataFetch';
import DiaryCanvas from '../../_components/DiaryCanvas';
import HecDiaryLayout from '../../_components/HecDiaryLayout';
import FeedbackModal from '../../_components/FeedbackModal';
import { Icon } from '@iconify/react';
import { formatDate } from '@/utils/dateFormatter';
import EditorViewer from '@/components/EditorViewer';
import Image from 'next/image';
import { ButtonIcon } from '@/components/Button';

const MissionDiaryReviewPage = () => {
  const { id } = useParams();
  const router = useRouter();
  const [correctionText, setCorrection] = useState('');
  const [score, setScore] = useState('');
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const searchParams = useSearchParams();
  const tabParam = searchParams.get('tab');
  const [activeTab, setActiveTab] = useState(tabParam || 'missionDiary');

  useEffect(() => {
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [tabParam]);

  // Fetch mission diary entry data
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: 'mission-diary-entry-review',
    endPoint: `/diary/tutor/missions/entries/${id}`,
    // me
    params: {},
    enabled: !!id,
  });

  // Initialize correction text with original content when data is loaded
  useEffect(() => {
    if (data?.content) {
      setCorrection(data.content);
    }
  }, [data?.content]);

  // Submit review function
  const submitReview = async () => {
    if (!score) {
      toast.error('Please provide a score');
      return;
    }

    try {
      const response = await api.post(
        `/tutor/diary/mission/entries/${id}/correction`,
        {
          correctionText,
          score: parseInt(score),
        }
      );

      if (response.success) {
        toast.success('Review submitted successfully');
        router.push(
          '/dashboard/submission-management/hec-diary?tab=missionDiary'
        );
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      toast.error(err.message || 'Failed to submit review');
    }
  };

  const handleBack = () => {
    router.push('/dashboard/submission-management/hec-diary?tab=missionDiary');
  };

  if (isLoading) {
    return (
      <HecDiaryLayout activeTab={activeTab}>
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
        </div>
      </HecDiaryLayout>
    );
  }

  if (error) {
    return (
      <HecDiaryLayout activeTab={activeTab}>
        <div className="bg-red-100 text-red-700 p-4 rounded-md">
          Error loading diary entry: {error.message}
        </div>
      </HecDiaryLayout>
    );
  }

  return (
    <HecDiaryLayout activeTab={activeTab}>
      <div className="flex justify-between items-center mb-2">
        <div className="flex gap-4 items-center">
          <h6 className="text-lg text-gray-700 font-medium ">
            View Mission Diary
          </h6>
          <h2 className="text-[#464646] font-normal">
            {data?.diary?.userName}
          </h2>
        </div>
      </div>

      <div className="grid items-center bg-[#FFF9FB] gap-2 p-4 shadow-xl border rounded-lg space-y-3">
        {/* Diary Canvas */}
        <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]  flex items-center justify-between">
          <div>
            <h1 className="text-2xl text-yellow-800 font-semibold">
              {data?.mission?.title}
            </h1>
            <p>Instructions:</p>
            <EditorViewer data={data?.mission?.description} />
          </div>

          <h2 className="text-3xl font-semibold text-yellow-600 font-serif">
            Mission Diary
          </h2>
        </div>

        {/* Original Content */}
        <div>
          <h3 className="text-xl text-yellow-800 font-semibold mb-2">
            Student Submission
          </h3>
          <div className="rounded-md shadow border p-4 min-h-32 max-h-72 overflow-y-auto bg-white">
            <EditorViewer
              data={data?.content}
              className="whitespace-pre-wrap text-sm text-[#314158]"
            />
          </div>
        </div>

        {/* Correction Section */}
        <div className="overflow-y-auto">
          <div className="flex justify-between items-center">
            <h3 className="text-xl text-yellow-800 font-semibold mb-2">
              Tutuor Correction Zone
            </h3>
          </div>
          <textarea
            value={correctionText}
            onChange={(e) => setCorrection(e.target.value)}
            className="w-full min-h-[120px] p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500 text-sm"
          />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-5">
              <ButtonIcon
                icon={'arcticons:feedback-2'}
                innerBtnCls={'h-12 w-12'}
                btnIconCls={'h-5 w-5'}
                onClick={() => setIsFeedbackModalOpen(true)}
              />
              <div className="flex items-center">
                <label className="mr-2">Score:</label>
                <input
                  type="number"
                  value={score}
                  onChange={(e) => setScore(e.target.value)}
                  className="w-24 border border-gray-300 rounded px-2 py-1"
                  min="0"
                  max="100"
                  placeholder="0-100"
                />
              </div>
            </div>
            <div className='flex items-center gap-4'>
              <button onClick={() => handleBack()} className='px-4 py-2 bg-gray-400 text-white rounded-md'>
                  Cancel
              </button>
              <button
                onClick={submitReview}
                disabled={!correctionText.trim() || !score}
                className="px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                Submit Review
              </button>
            </div>
          </div>
        </div>
      </div>

      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        feedbacks={data?.feedbacks || []}
        onClose={() => setIsFeedbackModalOpen(false)}
        refetch={refetch}
        entryId={id}
      />
    </HecDiaryLayout>
  );
};

export default MissionDiaryReviewPage;
