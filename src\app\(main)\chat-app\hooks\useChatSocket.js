'use client';

import { useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import io from 'socket.io-client';
import Cookies from 'js-cookie';
import { API_BASE_URL } from '@/lib/config';
import {
  setIsConnected,
  addMessage,
  updateContact,
  setRemoteTyping,
  markMessagesAsDelivered,
  markMessagesAsRead,
  replaceOptimisticMessage,
  incrementUnreadCount,
  clearUnreadCount,
  selectConversationId,
  selectMe,
} from '@/store/features/chatSlice';

const SOCKET_URL = `${API_BASE_URL}/chat`;

export const useChatSocket = () => {
  const dispatch = useDispatch();
  const socket = useRef(null);
  const conversationId = useSelector(selectConversationId);
  const me = useSelector(selectMe);

  // Refs to avoid stale closures in socket callbacks
  const conversationIdRef = useRef(null);
  const meRef = useRef(null);

  const token = Cookies.get('token') || '';

  // Update refs when values change
  useEffect(() => {
    conversationIdRef.current = conversationId;
  }, [conversationId]);

  useEffect(() => {
    meRef.current = me;
  }, [me]);

  // Initialize socket connection
  useEffect(() => {
    if (!token) return;

    socket.current = io(SOCKET_URL, {
      auth: { token },
      query: { token },
      extraHeaders: { Authorization: `Bearer ${token}` }
    });

    // Connection events
    socket.current.on('connect', () => {
      dispatch(setIsConnected(true));
    });

    socket.current.on('disconnect', () => {
      dispatch(setIsConnected(false));
    });

    // Message events
    socket.current.on('new_message', (message) => {
      // Update contact's last message
      dispatch(updateContact({
        conversationId: message.conversationId,
        lastMessage: message.content,
        lastMessageTime: message.createdAt
      }));

      // Get current conversation ID from ref to avoid stale closure
      const currentConversationId = conversationIdRef.current;

      // Add message to current conversation if it matches
      if (message.conversationId === currentConversationId) {
        const mappedMessage = mapServerMessage(message);
        dispatch(addMessage(mappedMessage));
      } else {
        // Increment unread count for other conversations
        dispatch(incrementUnreadCount({ conversationId: message.conversationId }));
      }
    });

    // Typing events
    socket.current.on('typing', (data) => {
      const currentConversationId = conversationIdRef.current;
      if (data.conversationId === currentConversationId && data.isTyping) {
        dispatch(setRemoteTyping(true));
        setTimeout(() => dispatch(setRemoteTyping(false)), 1400);
      }
    });

    // Message status events
    socket.current.on('messages_delivered', (data) => {
      const currentConversationId = conversationIdRef.current;
      const currentMe = meRef.current;
      if (data.conversationId === currentConversationId) {
        dispatch(markMessagesAsDelivered({
          conversationId: data.conversationId,
          userId: currentMe?.id
        }));
      }
    });

    socket.current.on('messages_read', (data) => {
      const currentConversationId = conversationIdRef.current;
      const currentMe = meRef.current;
      if (data.conversationId === currentConversationId) {
        dispatch(markMessagesAsRead({
          conversationId: data.conversationId,
          userId: currentMe?.id
        }));
      }
    });

    return () => {
      if (socket.current) {
        socket.current.disconnect();
        dispatch(setIsConnected(false));
      }
    };
  }, [token, dispatch]);

  // Subscribe to conversation when it changes
  useEffect(() => {
    if (socket.current && conversationId) {
      socket.current.emit('subscribe_conversation', {
        conversationId
      });
    }
  }, [conversationId]);

  // Helper function to map server message format
  const mapServerMessage = (serverMessage) => ({
    id: serverMessage.id,
    conversationId: serverMessage.conversationId,
    content: serverMessage.content,
    sender: { id: serverMessage.senderId },
    createdAt: new Date(serverMessage.createdAt || Date.now()),
    status: serverMessage.status || 'sent',
    attachments: serverMessage.attachments || [],
  });

  // Socket methods
  const sendMessage = (messageData, callback) => {
    if (socket.current) {
      socket.current.emit('send_message', messageData, (ack) => {
        if (callback) callback(ack);
      });
    }
  };

  const sendTyping = (isTyping) => {
    if (socket.current && conversationId) {
      socket.current.emit('typing', {
        conversationId,
        isTyping
      });
    }
  };

  const subscribeToConversation = (convId) => {
    if (socket.current && convId) {
      socket.current.emit('subscribe_conversation', {
        conversationId: convId
      });
    }
  };

  return {
    socket: socket.current,
    sendMessage,
    sendTyping,
    subscribeToConversation,
    mapServerMessage,
  };
};
