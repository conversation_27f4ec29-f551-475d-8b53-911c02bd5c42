'use client';

import NewTablePage from '@/components/form/NewTablePage';
import ViewAwardModal from '../view-award/page'; // Import the extracted modal
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import Image from 'next/image';

const Awards = () => {
  const router = useRouter();
  const [search, setSearch] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [searchField, setSearchField] = useState('name');
  const [sortField, setSortField] = useState('');
  const [sortDirection, setSortDirection] = useState('asc');
  const [activeTab, setActiveTab] = useState('weekly'); // New state for active tab - default to weekly
  
  // Modal states
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewAwardData, setViewAwardData] = useState(null);
  const [fetchingAward, setFetchingAward] = useState(false);

  // Define frequency tabs - only weekly, monthly, yearly
  const frequencyTabs = [
    { key: 'weekly', label: 'Weekly Awards' },
    { key: 'monthly', label: 'Monthly Awards ' },
    { key: 'yearly', label: 'Annual Awards'}
  ];

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['awards', page, limit, search, activeTab],
    endPoint: `/awards/admin`,
    params: {
      page,
      limit,
      ...(search.length > 2 && { name: search }),
      frequency: activeTab // Always filter by frequency since we removed 'all' tab
    },
    enabled: search.length < 1 || search.length > 2
  });

  // Function to fetch a single award by ID
  const fetchAwardById = async (awardId) => {
    try {
      setFetchingAward(true);
      
      // API call to get the specific award
      const response = await api.get(`/awards/admin/${awardId}`);
      
      if (response && response.success) {
        console.log('Fetched award details:', response.data);
        return response.data;
      } else {
        console.error('Failed to fetch award details:', response?.message || 'Unknown error');
        return null;
      }
    } catch (error) {
      console.error('Error fetching award details:', error);
      return null;
    } finally {
      setFetchingAward(false);
    }
  };

  // Handle view action - show award details in modal
  const handleViewAward = async (award) => {
    console.log('Fetching award details for viewing:', award.id);
    const awardDetails = await fetchAwardById(award.id);
    
    if (awardDetails) {
      // Prepare data for the view modal
      setViewAwardData({
        name: awardDetails.name || award.name,
        rewardPoints: awardDetails.rewardPoints || award.rewardPoints,
        module: awardDetails.module || award.module,
        frequency: awardDetails.frequency || award.frequency,
        status: awardDetails.status || award.status,
        isActive: awardDetails.isActive !== undefined ? awardDetails.isActive : award.isActive,
        description: awardDetails.description || 'No description available',
        criteria: awardDetails.criteria || 'No criteria specified',
        createdAt: awardDetails.createdAt ? new Date(awardDetails.createdAt).toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        }) : 'N/A',
        updatedAt: awardDetails.updatedAt ? new Date(awardDetails.updatedAt).toLocaleDateString('en-US', {
          day: 'numeric',
          month: 'long',
          year: 'numeric'
        }) : 'N/A'
      });
      
      // Show the modal
      setShowViewModal(true);
    }
  };

  // Handle page changes
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  // Handle tab changes
  const handleTabChange = (tabKey) => {
    setActiveTab(tabKey);
    setPage(1); // Reset to first page when changing tabs
  };

  // Filter table data based on active tab frequency
 // Filter table data based on active tab frequency and module = 'diary'
const getFilteredTableData = () => {
  if (!data?.items) return [];
  
  return data.items
    .filter(item => {
      // Filter by frequency matching the active tab
      const itemFrequency = item.frequency?.toLowerCase();
      const frequencyMatch = itemFrequency === activeTab;
      
      // Filter by module = 'diary'
      const moduleMatch = item.module?.toLowerCase() === 'diary';
      
      // Return items that match both conditions
      return frequencyMatch && moduleMatch;
    })
    .map((item, index) => ({
      name: item.name,
      rewardPoints: item.rewardPoints,
      module: item.module,
      frequency: item.frequency,
      status: item.status,
      isActive: item.isActive,
      id: item.id
    }));
};

 

 
  const tableData = getFilteredTableData();

  const columns = [
    { label: 'Award Name', field: 'name', },
    // { label: 'User ID', field: 'id'},
    { label: 'Points', field: 'rewardPoints'},
    
    
  ];

  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: handleViewAward,
    },
  ];

  // Close view modal
  const handleCloseModal = () => {
    setShowViewModal(false);
    setViewAwardData(null);
  };

  // Name filter options for the dropdown
  const nameFilterOptions = [
    { label: 'Award Name', value: 'name' },
    { label: 'Module', value: 'module' },
    { label: 'Frequency', value: 'frequency' }
  ];

  // Handle search changes
  const handleSearch = (searchTerm) => {
    if (searchTerm.length > 2 || searchTerm.length === 0) {
      setSearch(searchTerm);
    }
  };

  // Handle name filter changes
  const handleNameFilterChange = (field) => {
    setSearchField(field);
  };

  // Handle sorting
  const handleSort = (field) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  return (
    <div className="p-6 bg-white">
      {/* Show loading indicator when fetching individual award */}
      {fetchingAward && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg">
            <p className="text-gray-700">Loading award data...</p>
          </div>
        </div>
      )}
      
      {/* View Award Modal - Now using the extracted component */}
      <ViewAwardModal
        isOpen={showViewModal}
        onClose={handleCloseModal}
        awardData={viewAwardData}
      />

      {/* Frequency Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8" aria-label="Tabs">
            {frequencyTabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => handleTabChange(tab.key)}
                className={`
                  whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200
                  ${activeTab === tab.key
                    ? 'border-yellow-500 text-black bg-[#FEFCE8]'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      <NewTablePage
        title= {false}
        createButton={false}
        createBtnLink="/dashboard/awards/add"
        columns={columns}
        actions={actions}
        data={tableData}
        loading={isLoading}
        currentPage={page}
        changePage={handlePageChange}
        totalItems={tableData.length} // Use filtered data length
        rowsPerPage={limit}
        setRowsPerPage={setLimit}
        // Search and filter props
        showSearch={true}
        showNameFilter={false}
        showSortFilter={false}
        showCheckboxes={false}
        onSearch={handleSearch}
        onNameFilterChange={handleNameFilterChange}
        onSort={handleSort}
        nameFilterOptions={nameFilterOptions}
        searchTerm={search}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}
      />
    </div>
  );
};

export default Awards;