import { ButtonIcon } from '@/components/Button';
import useDataFetch from '@/hooks/useDataFetch';
import React, { useState } from 'react';
import EssayFeedBackModal from '../../essay/_components/FeedbackModal';

const CorrectionSection = ({ subject, date, id }) => {
  const [showModal, setShowModal] = useState(false);
  const { data, isLoading } = useDataFetch({
    queryKey: ['novel-correction', id],
    endPoint: `/student/novel/entries/${id}`,
  });

  return (
    <div className="h-40 shadow-lg border rounded-lg p-2 relative">
      <div className="h-full">
        <p className="text-sm text-[#864D0D] text-center font-medium mb-2">
          Tutor Review Zone
        </p>
        <div className="flex justify-between items-center border-b-2 border-dashed border-gray-300 mb-3">
          <h3 className="mb-2">{subject}</h3>
          <div className="flex items-center gap-3 text-sm">{date}</div>
        </div>
        <div
          className="min-h-28"
          dangerouslySetInnerHTML={{
            __html: `${data?.correction?.correction || ''}`,
          }}
        />
      </div>

      <span className="absolute right-2 bottom-2">
        <ButtonIcon
          icon="tabler:message-2-star"
          innerBtnCls={`h-12 w-12`}
          btnIconCls={`h-5 w-5`}
          onClick={() => setShowModal(true)}
          aria-label=""
        />
      </span>

      <EssayFeedBackModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Teachers Correction"
        data={data?.correction?.correction}
      />
    </div>
  );
};

export default CorrectionSection;
