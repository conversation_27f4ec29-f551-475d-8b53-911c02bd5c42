'use client';
import useDataFetch from '@/hooks/useDataFetch';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useState, useEffect } from 'react';
import HecDiaryStudents from './_components/HecDiaryStudents';
import HecQAStudents from './_components/HecQAStudents';
import HecPlayStudents from './_components/HecPlayStudents';
import HecEssayStudents from './_components/HecEssayStudents';
import HecNovelStudents from './_components/HecNovelStudents';

const Students = () => {
    const router = useRouter();
    const searchParams = useSearchParams();
    const [activeTab, setActiveTab] = useState('hec_user_diary');
    const [isLoading, setIsLoading] = useState(true);

    const {data: students, isLoading: studentsLoading} = useDataFetch({
        queryKey: 'tutor-students',
        endPoint: '/tutor/students',
    });

    // Tab configuration for easier management
    const tabConfig = [
        { key: 'hec_user_diary', label: 'HEC Diary' },
        { key: 'english_qa_writing', label: 'HEC Q/A' },
        { key: 'hec_play', label: 'HEC Play' },
        { key: 'english_essay', label: 'HEC Essay' },
        { key: 'english_novel', label: 'HEC Novel' }
    ];

    // Initialize tab from URL parameter or default
    useEffect(() => {
        const validTabs = ['hec_user_diary', 'english_qa_writing', 'hec_play', 'english_essay', 'english_novel'];
        const tabFromUrl = searchParams.get('tab');
        if (tabFromUrl && validTabs.includes(tabFromUrl)) {
            setActiveTab(tabFromUrl);
        }
        setIsLoading(false);
    }, [searchParams]);

    // Handle tab change and update URL
    const handleTabChange = (tab) => {
        setActiveTab(tab);
        const params = new URLSearchParams(searchParams);
        params.set('tab', tab);
        router.push(`/dashboard/students?${params.toString()}`);
    };

    // Filter students by module type for the active tab
    const getStudentsForTab = (moduleType) => {
        if (!students) return [];
        return students.filter(student => student.moduleType === moduleType);
    };

    // Function to render the correct content based on active tab
    const renderContent = () => {
        if (isLoading || studentsLoading) {
            return (
                <div className="flex items-center justify-center h-full">
                    <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#FFDE34]"></div>
                        <span>Loading...</span>
                    </div>
                </div>
            );
        }

        const studentsForTab = getStudentsForTab(activeTab);

        switch(activeTab) {
            case 'hec_user_diary':
                return <HecDiaryStudents students={studentsForTab} />;
            case 'english_qa_writing':
                return <HecQAStudents students={studentsForTab} />;
            case 'hec_play':
                return <HecPlayStudents students={studentsForTab} />;
            case 'english_essay':
                return <HecEssayStudents students={studentsForTab} />;
            case 'english_novel':
                return <HecNovelStudents students={studentsForTab} />;
            default:
                return (
                    <div className="flex items-center justify-center h-full">
                        <div className="text-gray-500">Select a tab to get started</div>
                    </div>
                );
        }
    };

    return (
        <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-semibold">Student List</h1>
            </div>

            {/* Tab buttons */}
            <div className="flex border-b border-gray-200 mb-6">
                {tabConfig.map((tab, index) => (
                    <button
                        key={index}
                        className={`py-3 px-6 font-medium text-sm focus:outline-none ${
                            activeTab === tab.key
                                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]'
                                : 'text-gray-500 hover:text-gray-700'
                        }`}
                        onClick={() => handleTabChange(tab.key)}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>

            {/* Content area */}
            <div className="min-h-[400px]">
                {renderContent()}
            </div>
        </div>
    );
};

export default Students;