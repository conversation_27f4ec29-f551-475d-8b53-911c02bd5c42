'use client';
import React, { useEffect, useState } from 'react';
import Topbar from '../diary/_component/Topbar';
import { usePathname } from 'next/navigation';

const EssayLayout = ({ children }) => {
  const [pageTitle, setPageTitle] = useState('Essay');
  const [sortOption, setSortOption] = useState('newest');
  const [layoutBackground, setLayoutBackground] = useState('#FFFDF5');
  const pathname = usePathname();

  // Only fetch background color if we're on the main diary page
  useEffect(() => {
    const fetchBackgroundColor = async () => {
      // Only fetch background color for the main diary page or my diary page
      if (pathname !== '/diary' && pathname !== '/diary/my') return;

      try {
        console.log('Fetching background color for diary page');
        const response = await api.get('/diary/entries/today');
        if (
          response.success &&
          response.data &&
          response.data.backgroundColor
        ) {
          console.log(
            'Setting background color to:',
            response.data.backgroundColor
          );
          setLayoutBackground(response.data.backgroundColor);
        }
      } catch (error) {
        console.error('Error fetching background color:', error);
      }
    };

    fetchBackgroundColor();
  }, [pathname]);

  const handleBackgroundChange = (color) => {
    // Only apply background change if we're on the main diary page
    if (!pathname.includes('/essay')) return;

    // Apply the selected color directly
    if (color) {
      setLayoutBackground(color);

      // Store the color in localStorage for persistence
      localStorage.setItem('diaryLayoutBackground', color);

      // Make the color available to the WriteDiary component
      window.diaryLayoutBackground = color;

      // Dispatch a custom event that WriteDiary can listen for
      const event = new CustomEvent('layoutBackgroundChanged', {
        detail: color,
      });
      window.dispatchEvent(event);
    }
  };

  const handleSearch = (query) => {
    // Search implementation...
  };

  // Handle sort change
  const handleSortChange = (option) => {
    setSortOption(option);
  };
  return (
    <div
      className="min-h-[80vh] flex flex-col"
      style={{
        background:
          (pathname === '/eassy' || pathname.includes('/essay'))
            ? layoutBackground
            : '#FFFDF5',
        transition: 'background-color 0.3s',
      }}
    >
      {/* Topbar */}
      <div className='bg-[#8B4513]'>
        <Topbar
          title={pageTitle}
          onSearch={handleSearch}
          onSortChange={handleSortChange}
          showContainer={true}
          onBackgroundChange={
            (pathname === '/eassy' || pathname.includes('/essay'))
              ? handleBackgroundChange
              : null
          }
        />
      </div>

      <div className="">{children}</div>
    </div>
  );
};

export default EssayLayout;
