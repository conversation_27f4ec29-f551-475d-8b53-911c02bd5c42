'use client';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import DeleteModal from '@/components/form/modal/DeleteModal';
import ViewModal from './details/page'; // Import the new ViewModal
import api from '@/lib/api'; // Import api for PUT requests
import { toast } from 'sonner'; // Import toast for notifications

const MissionDiary = () => {
  const router = useRouter();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false); // Add state for view modal
  const [selectedMission, setSelectedMission] = useState(null);
  const [updatingStatus, setUpdatingStatus] = useState({});

  const { data, isLoading, refetch } = useDataFetch({
    queryKey: ['mission-diary', currentPage, rowsPerPage],
    endPoint: '/diary/admin/missions',
    params: { page: currentPage, limit: rowsPerPage },
  });

  const missionDiary = data?.items || [];

  // Function to toggle mission status
  const toggleMissionStatus = async (mission) => {
    const missionId = mission.id;
    setUpdatingStatus(prev => ({ ...prev, [missionId]: true }));

    const updatedMission = {
      title: mission.title,
      description: mission.description,
      targetWordCount: mission.targetWordCount,
      targetMaxWordCount: mission.targetMaxWordCount,
      publishDate: mission.publishDate,
      expiryDate: mission.expiryDate,
      isActive: !mission.isActive, // Toggle the status
      score: mission.score
    };

    try {
      // Use api.put directly instead of useDataFetch mutation
      await api.put(`/diary/admin/missions/${missionId}`, updatedMission);
      
      // Refresh the data after successful update
      refetch();
      
      console.log('Mission status updated successfully');
    } catch (error) {
      console.error('Error in toggle function:', error);
      
      // Show error message
      toast.error(
        error?.response?.data?.message || 'Failed to update mission status'
      );
    } finally {
      setUpdatingStatus(prev => ({ ...prev, [missionId]: false }));
    }
  };

  // Pagination logic
  const totalItems = data?.meta?.totalItems || missionDiary.length;
  const paginatedQuestions = missionDiary;

  // Define columns for the table
  const columns = [
    {
      label: 'MISSION TITTLE',
      field: 'title',
    },
    {
      label: 'MISSION STATUS',
      field: 'isActive',
      cellRenderer: (value, row) => (
        <div className="">
          {/* Enhanced Toggle Switch with Tooltip */}
          <div className="relative group">
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={value}
                onChange={() => toggleMissionStatus(row)}
                disabled={updatingStatus[row.id]}
                className="sr-only peer"
              />
              <div className={`relative w-12 h-6 rounded-full transition-all duration-300 ease-in-out ${
                value 
                  ? 'bg-green-500 shadow-lg shadow-green-200' 
                  : 'bg-gray-300'
              } peer-focus:outline-none peer-focus:ring-4 ${
                value ? 'peer-focus:ring-green-200' : 'peer-focus:ring-gray-200'
              } peer-disabled:opacity-50`}>
                <div className={`absolute top-0.5 left-0.5 bg-white rounded-full h-5 w-5 transition-all duration-300 ease-in-out shadow-md ${
                  value ? 'transform translate-x-6' : ''
                }`}></div>
              </div>
            </label>
            
            {/* Tooltip */}
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap pointer-events-none z-10">
              {value ? 'Active - Click to deactivate' : 'Inactive - Click to activate'}
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
            </div>
          </div>
          
          {/* Loading spinner */}
          {updatingStatus[row.id] && (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-gray-300 border-t-green-500"></div>
          )}
        </div>
      ),
    },
  ];

  const actions = [
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (row) => {
        setSelectedMission(row);
        setShowViewModal(true);
      },
    },
    {
      name: 'edit',
      icon: 'material-symbols:edit-outline',
      className: 'text-gray-600',
      onClick: (row) =>
        router.push(
          `/dashboard/module-management/hec-diary/mission-diary/edit/${row?.id}`
        ),
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600',
      onClick: (row) => {
        setSelectedMission(row);
        setShowDeleteModal(true);
      },
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  // Handle edit from view modal
  const handleEditFromModal = (mission) => {
    setShowViewModal(false);
    router.push(
      `/dashboard/module-management/hec-diary/mission-diary/edit/${mission.id}`
    );
  };

  return (
    <div>
      <NewTablePage
        title="Mission Diary List"
        createButton="Add Mission"
        createBtnLink="/dashboard/module-management/hec-diary/mission-diary/add"
        columns={columns}
        data={paginatedQuestions}
        actions={actions}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={Math.ceil(totalItems / rowsPerPage)}
        showCheckboxes={false}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        showCreateButton={true}
        isLoading={isLoading}
      />

      {/* View Modal */}
      {showViewModal && (
        <ViewModal
          isOpen={showViewModal}
          onClose={() => setShowViewModal(false)}
          mission={selectedMission}
          onEdit={handleEditFromModal}
        />
      )}

      {/* Delete Modal */}
      {showDeleteModal && (
        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          data={selectedMission}
          endPoint={`/diary/admin/missions/${selectedMission?.id}`}
          onSuccess={refetch}
        />
      )}
    </div>
  );
};

export default MissionDiary;