'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  selectFilteredContacts,
  selectConversationId,
  selectIsLoadingContacts,
  selectContacts,
  setActiveContact,
  setConversationId,
} from '@/store/features/chatSlice';
import SearchBar from './SearchBar';

const ContactList = ({ onContactSelect }) => {
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();

  const contacts = useSelector(selectFilteredContacts);
  const allContacts = useSelector(selectContacts);
  const currentConversationId = useSelector(selectConversationId);
  const isLoading = useSelector(selectIsLoadingContacts);

  // Handle URL conversation ID parameter
  useEffect(() => {
    const urlConversationId = searchParams.get('conversationId');
    if (
      urlConversationId &&
      urlConversationId !== currentConversationId &&
      allContacts.length > 0
    ) {
      const contact = allContacts.find(
        (c) => c.conversationId === urlConversationId
      );
      if (contact && onContactSelect) {
        onContactSelect(contact);
      }
    }
  }, [searchParams, currentConversationId, allContacts, onContactSelect]);

  const handleContactClick = (contact) => {
    // Update Redux state
    dispatch(setActiveContact(contact));
    dispatch(setConversationId(contact.conversationId));

    // Update URL with conversation ID
    const params = new URLSearchParams(searchParams);
    params.set('conversationId', contact.conversationId);
    router.push(`/chat-app?${params.toString()}`, { scroll: false });

    // Call parent callback if provided
    if (onContactSelect) {
      onContactSelect(contact);
    }
  };

  const formatTime = (timestamp) => {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
      });
    } else if (diffInHours < 168) {
      // Less than a week
      return date.toLocaleDateString('en-US', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    }
  };

  if (isLoading) {
    return (
      <aside
        style={{
          width: 270,
          background: '#fff',
          borderRight: '1px solid #e5e7eb',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <div style={{ color: '#6b7280', fontSize: '14px' }}>
          Loading contacts...
        </div>
      </aside>
    );
  }

  return (
    <aside
      style={{
        width: 270,
        background: '#fff',
        borderRight: '1px solid #e5e7eb',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <SearchBar />

      <div style={{ flex: 1, overflowY: 'auto' }}>
        {contacts.length === 0 ? (
          <div
            style={{
              padding: '20px',
              textAlign: 'center',
              color: '#6b7280',
              fontSize: '14px',
            }}
          >
            No contacts found
          </div>
        ) : (
          contacts.map((contact) => (
            <div
              key={contact.conversationId}
              onClick={() => handleContactClick(contact)}
              style={{
                padding: '14px 16px',
                cursor: 'pointer',
                background:
                  contact.conversationId === currentConversationId
                    ? '#f1f5f9'
                    : 'transparent',
                borderBottom: '1px solid #f3f4f6',
                transition: 'background-color 0.2s',
              }}
              onMouseEnter={(e) => {
                if (contact.conversationId !== currentConversationId) {
                  e.target.style.backgroundColor = '#f9fafb';
                }
              }}
              onMouseLeave={(e) => {
                if (contact.conversationId !== currentConversationId) {
                  e.target.style.backgroundColor = 'transparent';
                }
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                }}
              >
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    flex: 1,
                    minWidth: 0,
                  }}
                >
                  <div
                    style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      overflow: 'hidden',
                    }}
                  >
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        background: `url(${
                          contact.profilePicture || '/assets/images/all-img/avatar.png'
                        }) center/cover no-repeat`,
                        position: 'relative',
                      }}
                    >
                      <Image
                        src={
                          contact.profilePicture ||
                          '/assets/images/all-img/avatar.png'
                        }
                        alt={contact.name}
                        width={32}
                        height={32}
                        style={{
                          objectFit: 'cover',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          opacity: 0,
                        }}
                        onError={(e) => {
                          e.target.style.opacity = 0;
                          e.target.parentElement.style.backgroundImage = `url('/assets/images/all-img/avatar.png')`;
                        }}
                      />
                    </div>
                  </div>
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <div
                      style={{
                        fontWeight: contact.unreadCount > 0 ? 700 : 500,
                        fontSize: '14px',
                        color: '#111827',
                        marginBottom: '4px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {contact.name}
                    </div>
                    <div
                      style={{
                        fontSize: '12px',
                        color: '#6b7280',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {contact.lastMessage || 'No messages yet'}
                    </div>
                  </div>
                  {contact.unreadCount > 0 && (
                    <div
                      style={{
                        backgroundColor: '#ef4444',
                        color: 'white',
                        borderRadius: '50%',
                        width: '20px',
                        height: '20px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '11px',
                        fontWeight: 'bold',
                        marginLeft: '8px',
                        flexShrink: 0,
                      }}
                    >
                      {contact.unreadCount > 9 ? '9+' : contact.unreadCount}
                    </div>
                  )}
                </div>
                {contact.lastMessageTime && (
                  <div
                    style={{
                      fontSize: '11px',
                      color: '#9ca3af',
                      marginLeft: '8px',
                      flexShrink: 0,
                    }}
                  >
                    {formatTime(contact.lastMessageTime)}
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </aside>
  );
};

export default ContactList;
