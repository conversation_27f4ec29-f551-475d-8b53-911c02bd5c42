'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import api from '@/lib/api';
import { toast } from 'react-toastify';
import { Icon } from '@iconify/react';
import { useSelector } from 'react-redux';

const QAMissionView = () => {
  const router = useRouter();
  const params = useParams();
  const missionId = params.id;
  
  // State variables
  const [mission, setMission] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
const auth = useSelector((state) => state.auth);
const getQAMissionDetailEndpoint = (roles, id) =>
  roles?.includes('tutor') ? `/tutor/qa-mission/${id}` : `/admin/qa-mission/${id}`;
  // Fetch mission details
  const fetchMissionDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      
const response = await api.get(
  getQAMissionDetailEndpoint(auth?.user?.roles, missionId)
);      
      if (response && response.success) {
        setMission(response.data);
        console.log('Mission details:', response.data);
      } else {
        setError(response?.message || 'Failed to fetch mission details');
        toast.error('Failed to load mission details');
      }
    } catch (error) {
      console.error('Error fetching mission details:', error);
      setError('An error occurred while fetching mission details');
      toast.error('An error occurred while loading mission details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (missionId) {
      fetchMissionDetails();
    }
  }, [missionId]);

  // Navigate back to mission list
  const handleBack = () => {
    router.push('/dashboard/module-management/hec-qa');
  };

  // Navigate to edit page
  const handleEdit = () => {
    // Store mission details in sessionStorage for editing
    sessionStorage.setItem('editMissionData', JSON.stringify(mission));
    router.push(`/dashboard/module-management/hec-qa/create-mission-qa?id=${missionId}`);
  };

  // Format date helper
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get frequency display text
  const getFrequencyDisplay = (timeFrequency, sequenceNumber) => {
    if (timeFrequency === 'weekly') {
      return `Week ${sequenceNumber}`;
    } else if (timeFrequency === 'monthly') {
      const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
      const monthName = monthNames[(sequenceNumber - 1) % 12];
      return monthName;
    }
    return `${timeFrequency} - ${sequenceNumber}`;
  };

  // Loading state
  if (loading) {
    return (
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-center h-64">
          <div className="flex flex-col items-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">Loading mission details...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !mission) {
    return (
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="text-center py-8">
          <div className="text-red-500 mb-4">
            <Icon icon="heroicons-outline:exclamation-triangle" className="w-12 h-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Mission</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <div className="flex justify-center space-x-4">
            <button
              onClick={fetchMissionDetails}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Try Again
            </button>
            <button
              onClick={handleBack}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleBack}
            className="flex items-center text-gray-600 hover:text-gray-900 focus:outline-none"
          >
            <Icon icon="heroicons-outline:arrow-left" className="w-5 h-5 mr-2" />
            Back to Mission List
          </button>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleEdit}
            className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
            title="Edit Mission"
          >
            <Icon icon="heroicons-outline:pencil" className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Mission Overview */}
      <div className="mb-8">
        <h1 className="text-2xl font-semibold text-gray-900 mb-4">Q&A Mission Details</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Mission ID</h3>
            <p className="text-sm font-mono text-gray-900 break-all">{mission.id}</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Time Frequency</h3>
            <p className="text-lg font-semibold text-gray-900 capitalize">{mission.timeFrequency}</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-500 mb-1">
              {mission.timeFrequency === 'weekly' ? 'Week' : 'Month'}
            </h3>
            <p className="text-lg font-semibold text-gray-900">
              {getFrequencyDisplay(mission.timeFrequency, mission.sequenceNumber)}
            </p>
          </div>
          
         
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Total Tasks</h3>
            <p className="text-2xl font-bold text-blue-600">{mission.tasks?.length || 0}</p>
          </div>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="text-sm font-medium text-gray-500 mb-1">Total Score</h3>
            <p className="text-2xl font-bold text-green-600">
              {mission.tasks?.reduce((sum, task) => sum + (task.totalScore || 0), 0) || 0}
            </p>
          </div>
        </div>
      </div>

      {/* Tasks Section */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Tasks</h2>
        
        {mission.tasks && mission.tasks.length > 0 ? (
          <div className="space-y-6">
            {mission.tasks
              .sort((a, b) => (a.sequence || 0) - (b.sequence || 0))
              .map((task, index) => (
                <div key={task.id} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                          Task {task.sequence || index + 1}
                        </span>
                        
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">{task.title}</h3>
                      <p className="text-gray-600 mb-4">{task.description}</p>
                    </div>
                    <div className="ml-4 text-right">
                      <div className="text-sm text-gray-500">Score</div>
                      <div className="text-lg font-bold text-green-600">{task.totalScore || 0}</div>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Word Limit</h4>
                      <p className="text-sm text-gray-900">
                        {task.wordLimitMinimum} - {task.wordLimitMaximum} words
                      </p>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Deadline</h4>
                      <p className="text-sm text-gray-900">{task.deadline} days</p>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Created</h4>
                      <p className="text-sm text-gray-900">{formatDate(task.createdAt)}</p>
                    </div>
                  </div>
                  
                  {task.instructions && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-2">Instructions</h4>
                      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                        <p className="text-sm text-gray-800">{task.instructions}</p>
                      </div>
                    </div>
                  )}
                </div>
              ))
            }
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500 border border-gray-200 rounded-lg">
            <Icon icon="heroicons-outline:clipboard-list" className="w-12 h-12 mx-auto mb-3 text-gray-400" />
            <p>No tasks found for this mission</p>
          </div>
        )}
      </div>

    </div>
  );
};

export default QAMissionView;