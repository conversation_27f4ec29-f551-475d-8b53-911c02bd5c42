'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import QuestionBank from './question-bank/page';
import CreateQuestions from './create-questions/page';
import MissionQAList from './mission-qa-List/page';
import CreateMission from './create-mission-qa/page';
import { Icon } from '@iconify/react';

function HecQA() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('missionQAList');
  const [isLoading, setIsLoading] = useState(true);
  
  // Define valid tabs
  const validTabs = ['questionBank', 'createQuestions', 'missionQAList', 'createMissionQA'];
  
  // Initialize active tab from URL or localStorage on component mount
  useEffect(() => {
    // First check URL params
    const tabFromUrl = searchParams.get('tab');
    
    // Then check localStorage as fallback
    const tabFromStorage = typeof window !== 'undefined' ? localStorage.getItem('hecqa-active-tab') : null;
    
    // Determine which tab to use
    let initialTab = 'missionQAList'; // default
    
    if (tabFromUrl && validTabs.includes(tabFromUrl)) {
      initialTab = tabFromUrl;
    } else if (tabFromStorage && validTabs.includes(tabFromStorage)) {
      initialTab = tabFromStorage;
    }
    
    setActiveTab(initialTab);
    setIsLoading(false);
    
    // Update URL if it doesn't match the active tab
    if (tabFromUrl !== initialTab) {
      const newUrl = new URL(window.location);
      newUrl.searchParams.set('tab', initialTab);
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [searchParams]);
  
  // Function to handle tab changes
  const handleTabChange = (newTab) => {
    if (!validTabs.includes(newTab)) return;
    
    setActiveTab(newTab);
    
    // Update URL
    const newUrl = new URL(window.location);
    newUrl.searchParams.set('tab', newTab);
    window.history.pushState({}, '', newUrl.toString());
    
    // Save to localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('hecqa-active-tab', newTab);
    }
  };
  
  // Function to handle back button click
  const handleBackClick = () => {
    // Clear the stored tab when going back
    if (typeof window !== 'undefined') {
      localStorage.removeItem('hecqa-active-tab');
    }
    window.history.back();
  };
  
  // Function to render the correct content based on active tab
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#FFDE34]"></div>
            <span>Loading...</span>
          </div>
        </div>
      );
    }
    
    switch(activeTab) {
      case 'questionBank':
        return <QuestionBank />;
      case 'createQuestions':
        return <CreateQuestions />;
      case 'missionQAList':
        return <MissionQAList />;
      case 'createMissionQA':
        return <CreateMission />;
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <div className="text-gray-500">Select a tab to get started</div>
          </div>
        );
    }
  };
  
  // Tab configuration for easier management
  const tabConfig = [
    { key: 'questionBank', label: 'Question Bank', icon: 'mdi:help-circle-outline' },
    { key: 'createQuestions', label: 'Create Questions', icon: 'mdi:plus-circle-outline' },
    { key: 'missionQAList', label: 'Mission Q & A List', icon: 'mdi:format-list-bulleted' },
    { key: 'createMissionQA', label: 'Create Mission Q & A', icon: 'mdi:plus-box-outline' }
  ];
  
  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header with Back Button and Title */}
      <div className="p-4 bg-white shadow-sm flex items-center border-b">
        {/* Back Arrow */}
        <button 
          onClick={handleBackClick}
          className="p-2 rounded-full hover:bg-gray-100 transition-colors mr-4 flex items-center justify-center"
          aria-label="Go back"
        >
          <Icon icon="mdi:arrow-left" className="text-xl text-gray-600" />
        </button>
        <div className="text-xl font-semibold text-gray-800">
          HEC Q & A
        </div>
        
        {/* Active tab indicator in header */}
        <div className="ml-auto text-sm text-gray-500">
          Current: {tabConfig.find(tab => tab.key === activeTab)?.label}
        </div>
      </div>
      
      {/* Main Content Area with Sidebar and Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <div className="w-64 bg-[#FEFCE8] p-4 flex flex-col shadow-sm">
          <div className="space-y-1">
            {tabConfig.map((tab) => (
              <button
                key={tab.key}
                className={`w-full p-3 text-left rounded-lg transition-all duration-200 flex items-center space-x-3 ${
                  activeTab === tab.key 
                    ? 'bg-[#FFDE34] text-black shadow-sm font-medium' 
                    : 'hover:bg-yellow-100 text-gray-700 hover:text-black'
                }`}
                onClick={() => handleTabChange(tab.key)}
              >
                <Icon icon={tab.icon} className="text-lg" />
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
          
          {/* Sidebar footer with current tab info */}
          <div className="mt-auto pt-4 border-t border-yellow-200">
            <div className="text-xs text-gray-600 text-center">
              Tab state is automatically saved
            </div>
          </div>
        </div>
        
        {/* Content Area */}
        <div className="flex-1 p-6 overflow-auto bg-white">
          {renderContent()}
        </div>
      </div>
    </div>
  );
}

export default HecQA;