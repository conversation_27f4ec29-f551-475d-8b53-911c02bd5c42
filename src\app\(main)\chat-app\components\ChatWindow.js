'use client';

import React from 'react';
import Image from 'next/image';
import { useSelector } from 'react-redux';
import {
  selectActiveContact,
  selectConversationId,
} from '@/store/features/chatSlice';
import MessageList from './MessageList';
import MessageInput from './MessageInput';
import EmptyChat from './EmptyChat';

const ChatWindow = ({ onSendMessage, onFileSelect, onTyping }) => {
  const activeContact = useSelector(selectActiveContact);
  const conversationId = useSelector(selectConversationId);

  // Show empty chat if no conversation is selected
  if (!activeContact || !conversationId) {
    return <EmptyChat />;
  }

  return (
    <section
      style={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        background: '#fff',
      }}
    >
      {/* Chat <PERSON>er */}
      <div
        style={{
          height: '70px',
          display: 'flex',
          alignItems: 'center',
          padding: '0 20px',
          borderBottom: '1px solid #e5e7eb',
          background: '#fff',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
        }}
      >
        <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{ width: '32px', height: '32px', borderRadius: '50%', overflow: 'hidden' }}>
            <div style={{
              width: '100%',
              height: '100%',
              background: `url(${activeContact.profilePicture || '/assets/images/all-img/avatar.png'}) center/cover no-repeat`,
              position: 'relative'
            }}>
              <Image
                src={activeContact.profilePicture || '/assets/images/all-img/avatar.png'}
                alt={activeContact.name}
                width={32}
                height={32}
                style={{
                  objectFit: 'cover',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  opacity: 0
                }}
                onError={(e) => {
                  e.target.style.opacity = 0;
                  e.target.parentElement.style.backgroundImage = `url('/assets/images/all-img/avatar.png')`;
                }}
              />
            </div>
          </div>
          <div>
            <h2
              style={{
                fontSize: '16px',
                fontWeight: '600',
                color: '#111827',
                margin: 0,
              }}
            >
              {activeContact.name}
            </h2>
          {activeContact.email && (
            <p
              style={{
                fontSize: '12px',
                color: '#6b7280',
                margin: 0,
                marginTop: '2px',
              }}
            >
              {activeContact.email}
            </p>
          )}
          </div>
        </div>
        
        {/* Optional: Add status indicator or actions */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          {/* Online status indicator */}
          <div
            style={{
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              background: activeContact.isOnline ? '#10b981' : '#d1d5db',
            }}
            title={activeContact.isOnline ? 'Online' : 'Offline'}
          />
          
          {/* Optional: More actions button */}
          <button
            style={{
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              padding: '4px',
              borderRadius: '4px',
              color: '#6b7280',
              fontSize: '16px',
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#f3f4f6';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = 'transparent';
            }}
            title="More options"
          >
            ⋮
          </button>
        </div>
      </div>

      {/* Messages Area */}
      <MessageList />

      {/* Message Input */}
      <MessageInput
        onSendMessage={onSendMessage}
        onFileSelect={onFileSelect}
        onTyping={onTyping}
      />
    </section>
  );
};

export default ChatWindow;
