'use client';
import FormInput from '@/components/form/FormInput';
import api from '@/lib/api';
import { Icon } from '@iconify/react';
import { Field, Form, Formik } from 'formik';
import Link from 'next/link';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import * as Yup from 'yup';

const validationSchema = Yup.object().shape({
  oldPassword: Yup.string().required('Please enter your old password'),
  password: Yup.string()
    .required('Please enter a new password')
    .min(8, 'Password must be at least 8 characters')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special (@, $, !, %, *, ?, or &) character'
    ),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password'), null], 'Passwords must match')
    .required('Please confirm your new password'),
});

const ChangePassword = () => {
  const [showPassword, setShowPassword] = useState(false);
  const { user } = useSelector((state) => state.auth);

  const handleSubmit = async (values) => {
    try {
      // const response = await api.post('/auth/change-password', values);
      console.log(values);
    } catch (error) {
      console.log(error);
    }
  };
  return (
    <div className="flex flex-col items-center">
      <div className="w-full max-w-md bg-white rounded-lg">
        <div className="px-4 py-6 pb-0 space-y-4">
          <h1 className="text-xl font-bold text-gray-900 text-center">
            HELLO ENGLISH COACHING - HEC
          </h1>

          {/* <h2 className="text-lg font-semibold text-gray-800">LOGIN</h2> */}

          <p className="text-gray-600 text-center">Change your password</p>

          <Formik
            initialValues={{
              email: user?.email,
              oldPassword: '',
              password: '',
              confirmPassword: '',
              rememberMe: false,
            }}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting, errors, touched, values }) => (
              <Form className="space-y-5">
                {errors.general && (
                  <div className="text-red-500 text-sm text-center">
                    {errors.general}
                  </div>
                )}

                <div className="">
                  <FormInput
                    label="Old Password"
                    name="oldPassword"
                    type="text"
                    placeholder="Enter old password"
                    required
                  />
                </div>
                <div className="relative">
                  <FormInput
                    label="Password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Enter new password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-[42px] transform text-gray-500"
                  >
                    <Icon
                      icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'}
                      width="16"
                      height="16"
                    />
                  </button>
                </div>
                <div className="relative">
                  <FormInput
                    label="Confirm Password"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="Confirm password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-[42px] transform text-gray-500"
                  >
                    <Icon
                      icon={showPassword ? 'mdi:eye-off' : 'mdi:eye'}
                      width="16"
                      height="16"
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between">
                  <label className="flex items-center cursor-pointer">
                    <Field
                      type="checkbox"
                      name="rememberMe"
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-600">
                      Remember Me
                    </span>
                  </label>
                  <Link
                    href="/forgot-password"
                    className="text-sm text-red-500 hover:text-red-600"
                  >
                    Forgot Password?
                  </Link>
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting || !values.userId || !values.password}
                  className={`w-full py-2 px-4 rounded-md transition-colors ${
                    isSubmitting || !values.userId || !values.password
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-yellow-300 text-gray-800 hover:bg-yellow-400'
                  }`}
                >
                  {isSubmitting ? 'Updating...' : 'Update Password'}
                </button>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default ChangePassword;
