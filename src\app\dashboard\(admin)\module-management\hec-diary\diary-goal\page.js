'use client';
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Edit, Trash2, Eye } from 'lucide-react';
import NewTablePage from "@/components/form/NewTablePage";
import DeleteModal from '@/components/form/modal/DeleteModal';
import ViewModal from './view/page'; // Import the ViewModal
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api'; 

// DiaryGoal component that uses createBtnLink for navigation
const DiaryGoal = () => {
  const router = useRouter();
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Fetch data from API
  const { data, isLoading, error, refetch } = useDataFetch({
    queryKey: ['/admin/diary/settings', currentPage, rowsPerPage],
    endPoint: '/admin/diary/settings',
    params: { page: currentPage, limit: rowsPerPage },
    enabled: true
  });

  // Debug logging
  console.log('Full API Response:', data);
  console.log('Is Loading:', isLoading);
  console.log('Error:', error);

  // Extract items and pagination info from API response
  let apiItems = [];
  let totalItems = 0;
  let totalPages = 1;

  if (data) {
    // Check if data has a nested data property
    if (data.data && data.data.items) {
      apiItems = data.data.items;
      totalItems = data.data.totalItems || data.data.totalCount || 0;
      totalPages = data.data.totalPages || 1;
    }
    // Fallback to direct items property
    else if (data.items) {
      apiItems = data.items;
      totalItems = data.totalItems || data.totalCount || 0;
      totalPages = data.totalPages || 1;
    }
  }

  // Sort items by level for better display order
  const sortedApiItems = [...apiItems].sort((a, b) => {
    // Primary sort by level
    if (a.level !== b.level) {
      return a.level - b.level;
    }
    // Secondary sort by title if levels are the same
    return a.title.localeCompare(b.title);
  });

  console.log('Full API Response Structure:', JSON.stringify(data, null, 2));
  console.log('Processed API Items:', sortedApiItems);
  console.log('Total Items:', totalItems);
  console.log('API Items Length:', sortedApiItems.length);

  // Define columns for the table
  const columns = [
   {
    label: 'Stage',
    field: 'level',
    key: 'level',
    cellRenderer: (...args) => {
      console.log('Stage cellRenderer args:', args);
      const [value, row, field] = args;
      
      // Try different approaches based on how the data is passed
      if (row && row.level) {
        return `Stage ${row.level}`;
      } else if (value) {
        return `Stage ${value}`;
      } else if (args[0] && args[0].level) {
        return `Stage ${args[0].level}`;
      }
      
      return `Stage N/A`;
    }
  },
   
    {
      label: 'Word Count',
      field: 'wordLimit',
      key: 'wordLimit',
      
    },
   
    {
      label: 'Action',
      field: 'actions',
      key: 'actions',
      cellRenderer: (value, row) => (
        <div className="flex gap-3 items-center">
          <button
            onClick={() => handleView(row)}
            className="text-green-600 hover:text-green-800 hover:bg-green-50 rounded-full p-1 transition-colors"
            title="View Details"
          >
            <Eye size={16} />
          </button>
          <button
            onClick={() => handleEdit(row)}
            className="text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-full p-1 transition-colors"
            title="Edit"
          >
            <Edit size={16} />
          </button>
          <button
            onClick={() => handleDelete(row)}
            className="text-red-600 hover:text-red-800 hover:bg-red-50 rounded-full p-1 transition-colors"
            title="Delete"
          >
            <Trash2 size={16} />
          </button>
        </div>
      )
    },
  ];

  // Handle page change
  const handleChangePage = (page) => {
    setCurrentPage(page);
  };

  // Handle view action
  const handleView = (row) => {
    console.log('View item:', row);
    setSelectedItem(row);
    setShowViewModal(true);
  };

  // Handle edit action
  const handleEdit = (row) => {
    console.log('Edit item:', row);
    router.push(`/dashboard/module-management/hec-diary/diary-goal/edit/${row.id}`);
  };

  // Handle delete action
  const handleDelete = (row) => {
    console.log('Delete item:', row);
    setSelectedItem(row);
    setShowDeleteModal(true);
  };

  return (
    <div className="container mx-auto p-4">
      <NewTablePage
        title="Today's Diary Goal"
        createButton="Add Stage"
        createBtnLink="/dashboard/module-management/hec-diary/diary-goal/add"
        columns={columns}
        data={sortedApiItems}
        currentPage={currentPage}
        changePage={handleChangePage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={totalPages}
        isLoading={isLoading}
        showCheckboxes={false}
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        emptyMessage="No diary goal settings found"
        showRefresh={true}
        onRefresh={refetch}
      />

      {/* View Modal */}
      <ViewModal
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
        data={selectedItem}
        title="Diary Goal Details"
      />

      {/* Delete Modal */}
      {showDeleteModal && (
        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          data={selectedItem}
          endPoint={`/admin/diary/settings/${selectedItem?.id}`}
          onSuccess={refetch}
        />
      )}
    </div>
  );
};

export default DiaryGoal;