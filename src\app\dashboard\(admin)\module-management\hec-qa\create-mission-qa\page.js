'use client';

import React, { useState, useEffect } from 'react';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import api from '@/lib/api';
import { toast } from 'sonner';
import { useRouter, useSearchParams } from 'next/navigation';
import DeleteModal from '@/components/form/modal/DeleteModal';
import { useSelector } from 'react-redux';

const MissionQAEditor = ({ onSuccessfulSave }) => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const missionId = searchParams.get('id');
  const isEditMode = !!missionId;
  const auth = useSelector((state) => state.auth);
  const getQAMissionDetailEndpoint = (roles, id) =>
    roles?.includes('tutor')
      ? `/tutor/qa-mission/${id}`
      : `/admin/qa-mission/${id}`;

  const getQAMissionCreateEndpoint = (roles) =>
    roles?.includes('tutor')
      ? '/tutor/qa-mission/create-mission'
      : '/admin/qa-mission/create-mission';

  const getQAMissionUpdateEndpoint = (roles, id) =>
    roles?.includes('tutor')
      ? `/tutor/qa-mission/${id}`
      : `/admin/qa-mission/${id}`;

  const getQAMissionTaskDeleteEndpoint = (roles, taskId) =>
    roles?.includes('tutor')
      ? `/tutor/qa-mission/task/${taskId}`
      : `/admin/qa-mission/task/${taskId}`;

  // Add these state variables after your existing useState declarations
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedMission, setSelectedMission] = useState(null);
  const [taskIdToDelete, setTaskIdToDelete] = useState(null);

  // Time frequency options
  const timeFrequencyOptions = ['Weekly', 'Monthly'];

  // State for API data
  const [weekOptions, setWeekOptions] = useState([]);
  const [monthOptions, setMonthOptions] = useState([]);

  const [missions, setMissions] = useState([
    {
      id: 1,
      title: '',
      description: '',
      selectDay: 'Ex: 1 Day',
      wordLimitMin: '',
      wordLimitMax: '',
      missionDeadline: '',
      totalScore: '', // For direct input
      instruction: '',
    },
  ]);

  const resetForm = () => {
    setMissions([
      {
        id: 1,
        title: '',
        description: '',
        selectDay: 'Ex: 1 Day',
        wordLimitMin: '',
        wordLimitMax: '',
        missionDeadline: '',
        totalScore: '',
        instruction: '',
      },
    ]);
    setTimeFrequency('Weekly');
  };

  const [timeFrequency, setTimeFrequency] = useState('Weekly');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Fetch weeks data
  const { data: weeksData, isLoading: isLoadingWeeks } = useQuery({
    queryKey: ['weeks'],
    queryFn: async () => {
      const response = await api.get('/qa-mission/time/weeks');
      return response.data;
    },
    onError: (error) => {
      console.error('Error fetching weeks data:', error);
      toast.error('Failed to load weeks data');
    },
  });

  // Fetch months data
  const { data: monthsData, isLoading: isLoadingMonths } = useQuery({
    queryKey: ['months'],
    queryFn: async () => {
      const response = await api.get('/qa-mission/time/months');
      return response.data;
    },
    onError: (error) => {
      console.error('Error fetching months data:', error);
      toast.error('Failed to load months data');
    },
  });

  // Process weeks and months data when available
  useEffect(() => {
    if (weeksData) {
      // Format weeks data for dropdown
      const formattedWeeks = weeksData.map((week) => ({
        value: week.title,
        label: week.title,
        sequence: week.sequence,
        id: week.id, // Store the week ID
      }));
      setWeekOptions(formattedWeeks);
    }
  }, [weeksData]);

  useEffect(() => {
    if (monthsData) {
      // Format months data for dropdown
      const formattedMonths = monthsData.map((month) => ({
        value: month.title,
        label: month.title,
        sequence: month.sequence,
        id: month.id, // Store the month ID
      }));
      setMonthOptions(formattedMonths);
    }
  }, [monthsData]);

  // Query to fetch mission data when in edit mode
  const fetchMissionData = async () => {
    // First check if we have data in sessionStorage (set by the list component)
    const sessionData = sessionStorage.getItem('editMissionData');

    if (sessionData) {
      const parsedData = JSON.parse(sessionData);
      // Clear session storage to avoid stale data on future visits
      sessionStorage.removeItem('editMissionData');
      return parsedData;
    } else if (missionId) {
      // If not in session storage but we have an ID, fetch directly from API
      const response = await api.get(
        getQAMissionDetailEndpoint(auth?.user?.roles, missionId)
      );
      if (response && response.success) {
        return response.data;
      }
      throw new Error(response?.message || 'Failed to fetch mission data');
    }
    return null;
  };

  // Use react-query to handle mission fetching
  const { data: missionData, isLoading: isFetchingMission } = useQuery({
    queryKey: ['missionDetails', missionId],
    queryFn: fetchMissionData,
    enabled: isEditMode, // Only run query when in edit mode
    onError: (error) => {
      toast.error('Failed to load mission data: ' + error.message);
      router.push('/dashboard/module-management/hec-qa?tab=mission-qa-list');
    },
  });

  // Effect to populate form with existing mission data when in edit mode
  // Effect to populate form with existing mission data when in edit mode
  useEffect(() => {
    if (missionData && !isLoading) {
      setIsLoading(true);

      try {
        console.log('Populating form with mission data:', missionData);

        // Set time frequency
        if (missionData.timeFrequency) {
          // Capitalize first letter for UI display
          const formattedFrequency =
            missionData.timeFrequency.charAt(0).toUpperCase() +
            missionData.timeFrequency.slice(1);
          setTimeFrequency(formattedFrequency);
        }

        // Map mission tasks to form format
        if (missionData.tasks && missionData.tasks.length > 0) {
          const formattedMissions = missionData.tasks.map((task, index) => {
            // Default day selection
            let dayOrMonthSelection = 'Ex: 1 Day';

            // Instead of looking for weekId/monthId, use sequenceNumber to find the matching week/month
            const sequenceNumber = missionData.sequenceNumber;

            if (
              missionData.timeFrequency === 'weekly' &&
              weekOptions.length > 0
            ) {
              const matchingWeek = weekOptions.find(
                (week) => week.sequence === sequenceNumber
              );
              if (matchingWeek) {
                dayOrMonthSelection = matchingWeek.value;
              }
            } else if (
              missionData.timeFrequency === 'monthly' &&
              monthOptions.length > 0
            ) {
              const matchingMonth = monthOptions.find(
                (month) => month.sequence === sequenceNumber
              );
              if (matchingMonth) {
                dayOrMonthSelection = matchingMonth.value;
              }
            }

            return {
              id: index + 1,
              taskId: task.id, // Store the backend task ID for API operations
              title: task.title || '',
              description: task.description || '',
              selectDay: dayOrMonthSelection,
              wordLimitMin: task.wordLimitMinimum?.toString() || '',
              wordLimitMax: task.wordLimitMaximum?.toString() || '',
              missionDeadline: task.deadline?.toString() || '',
              totalScore: task.totalScore?.toString() || '',
              instruction: task.instructions || '',
            };
          });

          setMissions(formattedMissions);
        }
      } catch (error) {
        console.error('Error parsing mission data:', error);
        toast.error(
          'Error loading mission data. Some values may be incorrect.'
        );
      } finally {
        setIsLoading(false);
      }
    }
  }, [missionData, weekOptions, monthOptions]);
  // Create/Update mutation
  const saveMissionMutation = useMutation({
    mutationFn: async (formData) => {
      if (isEditMode) {
        return api.patch(
          getQAMissionUpdateEndpoint(auth?.user?.roles, missionId),
          formData
        );
      }
      return api.post(getQAMissionCreateEndpoint(auth?.user?.roles), formData);
    },

    onSuccess: () => {
      queryClient.invalidateQueries(['missionsList']);

      if (typeof onSuccessfulSave === 'function') {
        onSuccessfulSave();
      } else if (isEditMode) {
        router.push('/dashboard/module-management/hec-qa?tab=mission-qa-list');
      } else {
        // For new missions, reset the form and show success message
        resetForm();
        // toast.success('Mission created successfully! You can now create another one.');
      }
    },
    onError: (error) => {
      console.error(
        `Error ${isEditMode ? 'updating' : 'creating'} missions:`,
        error
      );
    },
    onSettled: () => {
      setIsSubmitting(false);
    },
  });

  const addMoreMission = () => {
    const newId =
      missions.length > 0 ? Math.max(...missions.map((m) => m.id)) + 1 : 1;
    setMissions([
      ...missions,
      {
        id: newId,
        taskId: null, // New tasks don't have a backend ID yet
        title: '',
        description: '',
        selectDay: 'Ex: 1 Day',
        wordLimitMin: '',
        wordLimitMax: '',
        missionDeadline: '',
        totalScore: '',
        instruction: '',
      },
    ]);
  };

  const removeMission = (id) => {
    // Only allow removal if there's more than one mission
    if (missions.length > 1) {
      const missionToRemove = missions.find((mission) => mission.id === id);

      // If we're in edit mode and the mission has a taskId (exists in backend), use modal
      if (isEditMode && missionToRemove && missionToRemove.taskId) {
        setSelectedMission(missionToRemove);
        setTaskIdToDelete(missionToRemove.taskId);
        setShowDeleteModal(true);
      } else {
        // For new missions with no backend ID, just confirm and remove from state
        if (window.confirm('Are you sure you want to remove this mission?')) {
          setMissions(missions.filter((mission) => mission.id !== id));
        }
      }
    } else {
      toast.warning('At least one mission is required');
    }
  };

  const handleInputChange = (id, field, value) => {
    setMissions(
      missions.map((mission) =>
        mission.id === id ? { ...mission, [field]: value } : mission
      )
    );
  };

  const handleCancel = () => {
    if (!isEditMode) {
      // Reset form only in create mode
      resetForm();
    }
    router.push('/dashboard/module-management/hec-qa?tab=mission-qa-list');
  };

  const validateForm = () => {
    for (const mission of missions) {
      if (!mission.title || !mission.description || !mission.instruction) {
        toast.error('Please fill in all required fields for each mission');
        return false;
      }

      // Validate word limits are numbers
      if (
        isNaN(Number(mission.wordLimitMin)) ||
        (mission.wordLimitMax && isNaN(Number(mission.wordLimitMax)))
      ) {
        toast.error('Word limits must be valid numbers');
        return false;
      }

      // Ensure minimum word limit is specified
      if (!mission.wordLimitMin || Number(mission.wordLimitMin) <= 0) {
        toast.error('Minimum word limit must be a positive number');
        return false;
      }

      // Validate that max word limit is greater than min (if specified)
      if (
        mission.wordLimitMax &&
        Number(mission.wordLimitMax) <= Number(mission.wordLimitMin)
      ) {
        toast.error(
          'Maximum word limit must be greater than minimum word limit'
        );
        return false;
      }

      // Validate mission deadline if provided
      if (mission.missionDeadline && isNaN(Number(mission.missionDeadline))) {
        toast.error('Mission deadline must be a valid number');
        return false;
      }
    }
    return true;
  };
  // Modified handleSave function with console logging to debug timeFrequency
  // Modified handleSave function with fixes
  const handleSave = () => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    // Debug: Log the current timeFrequency value
    console.log('Selected timeFrequency before formatting:', timeFrequency);

    // Get the selected week or month ID based on UI selection
    let weekId = null;
    let monthId = null;

    // Get the first mission to check the selected day/month
    const firstMission = missions[0];
    console.log('First mission selectDay:', firstMission.selectDay);
    console.log('Available weekOptions:', weekOptions);
    console.log('Available monthOptions:', monthOptions);

    if (timeFrequency === 'Weekly' && weekOptions.length > 0) {
      // Find the selected week option
      const selectedWeek = weekOptions.find(
        (week) => week.value === firstMission.selectDay
      );
      if (selectedWeek) {
        weekId = selectedWeek.id;
        console.log('Found weekId:', weekId);
      } else {
        console.error('No matching week found for:', firstMission.selectDay);
        toast.error('Please select a valid week');
        setIsSubmitting(false);
        return;
      }
    } else if (timeFrequency === 'Monthly' && monthOptions.length > 0) {
      // Find the selected month option
      const selectedMonth = monthOptions.find(
        (month) => month.value === firstMission.selectDay
      );
      if (selectedMonth) {
        monthId = selectedMonth.id;
        console.log('Found monthId:', monthId);
      } else {
        console.error('No matching month found for:', firstMission.selectDay);
        toast.error('Please select a valid month');
        setIsSubmitting(false);
        return;
      }
    }

    // Validate that we have the required ID
    if (timeFrequency === 'Weekly' && !weekId) {
      toast.error('Please select a week');
      setIsSubmitting(false);
      return;
    }

    if (timeFrequency === 'Monthly' && !monthId) {
      toast.error('Please select a month');
      setIsSubmitting(false);
      return;
    }

    // Format the data for API
    const formattedTasks = missions.map((mission) => {
      // Direct number input for deadline
      let deadlineNumber = mission.missionDeadline
        ? Number(mission.missionDeadline)
        : 7;

      // Create base task data
      const taskData = {
        title: mission.title,
        description: mission.description,
        wordLimitMinimum: Number(mission.wordLimitMin),
        wordLimitMaximum: mission.wordLimitMax
          ? Number(mission.wordLimitMax)
          : null,
        deadline: deadlineNumber,
        instructions: mission.instruction,
        totalScore: mission.totalScore ? Number(mission.totalScore) : null,
      };

      // If we're in edit mode and have the original task ID, include it
      if (isEditMode && mission.taskId) {
        taskData.id = mission.taskId;
      }

      return taskData;
    });

    // Build the payload
    const payload = {
      timeFrequency: timeFrequency.toLowerCase(),
      tasks: formattedTasks,
    };

    // Add appropriate ID based on time frequency
    if (timeFrequency === 'Weekly') {
      payload.weekId = weekId;
      // Don't set monthId to null, just omit it
    } else if (timeFrequency === 'Monthly') {
      payload.monthId = monthId;
      // Don't set weekId to null, just omit it
    }

    // Debug payload before sending
    console.log('Sending payload:', JSON.stringify(payload, null, 2));

    saveMissionMutation.mutate(payload);
  };

  // Show loading state when fetching data
  if (isEditMode && (isFetchingMission || isLoading)) {
    return (
      <div className="bg-white font-sans">
        <div className="container mx-auto px-4 py-6">
          <h1 className="card-title text-black text-xl">Edit Mission Q&A</h1>
          <div className="flex justify-center my-8">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"></div>
              <p className="mt-2 text-gray-600">Loading mission data...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state when fetching weeks and months data
  if (isLoadingWeeks || isLoadingMonths) {
    return (
      <div className="bg-white font-sans">
        <div className="container mx-auto px-4 py-6">
          <h1 className="card-title text-black text-xl">
            {isEditMode ? 'Edit Mission Q&A' : 'Create Mission Q&A'}
          </h1>
          <div className="flex justify-center my-8">
            <div className="text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"></div>
              <p className="mt-2 text-gray-600">Loading options data...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Add this function after your existing functions
  const refetch = () => {
    // Remove from local state after successful deletion
    setMissions(
      missions.filter((mission) => mission.taskId !== taskIdToDelete)
    );
    // Reset modal state
    setShowDeleteModal(false);
    setSelectedMission(null);
    setTaskIdToDelete(null);
  };

  return (
    <div className="bg-white font-sans">
      <div className="container mx-auto px-4 py-6">
        <h1 className="card-title text-black text-xl">
          {isEditMode ? 'Edit Mission Q&A' : 'Create Mission Q&A'}
        </h1>
        <form className="bg-white space-y-6">
          {/* Time Frequency selector */}
          {/* Time Frequency selector */}
          <div className="bg-[#FFFAC2] rounded-lg p-4 max-w-lg mx-auto">
            <div className="mb-4">
              <label
                htmlFor="time-frequency"
                className="block text-sm font-semibold text-black mb-2"
              >
                Time Frequency<span className="text-red-600">*</span>
              </label>
              <div className="relative">
                <select
                  id="time-frequency"
                  name="time-frequency"
                  value={timeFrequency}
                  onChange={(e) => setTimeFrequency(e.target.value)}
                  className={`w-full text-base rounded-md border border-gray-300 px-4 py-3 appearance-none ${
                    isEditMode ? 'bg-gray-100' : 'bg-white'
                  } focus:outline-none focus:ring-1 focus:ring-yellow-400`}
                  disabled={isEditMode} // Disable in edit mode
                >
                  {timeFrequencyOptions.map((option) => (
                    <option key={option} value={option}>
                      {option}
                    </option>
                  ))}
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                  <svg
                    className="fill-current h-5 w-5"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                  </svg>
                </div>
              </div>
              {isEditMode && (
                <p className="mt-1 text-sm text-gray-500">
                  Time frequency cannot be changed in edit mode
                </p>
              )}
            </div>
          </div>

          {/* Map through all missions and display each one */}
          {missions.map((mission, index) => (
            <div
              key={mission.id}
              className="bg-gray-50 p-6 rounded-lg relative"
            >
              {/* Close/Remove button */}
              {missions.length > 1 && (
                <button
                  type="button"
                  className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 focus:outline-none"
                  onClick={() => removeMission(mission.id)}
                  aria-label="Remove mission"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-6 w-6"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              )}

              {/* Mission number indicator */}
              <div className="mb-4 pb-2 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-800">
                  Mission Q&A {index + 1}
                </h2>
              </div>

              {/* Mission Title, Description, Select Day/Month row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label
                    htmlFor={`mission-title-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Mission Q&A Title<span className="text-red-600">*</span>
                  </label>
                  <input
                    id={`mission-title-${mission.id}`}
                    name={`mission-title-${mission.id}`}
                    type="text"
                    value={mission.title}
                    placeholder="Write here"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) =>
                      handleInputChange(mission.id, 'title', e.target.value)
                    }
                  />
                </div>
                <div>
                  <label
                    htmlFor={`description-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Description<span className="text-red-600">*</span>
                  </label>
                  <input
                    id={`description-${mission.id}`}
                    name={`description-${mission.id}`}
                    type="text"
                    value={mission.description}
                    placeholder="Write here"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) =>
                      handleInputChange(
                        mission.id,
                        'description',
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label
                    htmlFor={`select-day-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    {timeFrequency === 'Weekly'
                      ? 'Select Week'
                      : 'Select Month'}
                    <span className="text-red-600">*</span>
                  </label>
                  <div className="relative">
                    <select
                      id={`select-day-${mission.id}`}
                      name={`select-day-${mission.id}`}
                      value={mission.selectDay}
                      className={`w-full text-base rounded border border-gray-300 px-4 py-3 appearance-none focus:outline-none focus:ring-1 focus:ring-yellow-400 ${
                        isEditMode ? 'bg-gray-100' : 'bg-white'
                      }`}
                      onChange={(e) =>
                        handleInputChange(
                          mission.id,
                          'selectDay',
                          e.target.value
                        )
                      }
                      disabled={isEditMode} // Disable in edit mode
                    >
                      <option value="" disabled>
                        {timeFrequency === 'Weekly'
                          ? 'select days'
                          : 'select month'}
                      </option>
                      {timeFrequency === 'Weekly'
                        ? weekOptions.map((day) => (
                            <option key={day.value} value={day.value}>
                              {day.label}
                            </option>
                          ))
                        : monthOptions.map((month) => (
                            <option key={month.value} value={month.value}>
                              {month.label}
                            </option>
                          ))}
                    </select>
                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                      <svg
                        className="fill-current h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                      </svg>
                    </div>
                  </div>
                  {isEditMode && (
                    <p className="mt-1 text-sm text-gray-500">
                      {timeFrequency === 'Weekly' ? 'Week' : 'Month'} selection
                      cannot be changed in edit mode
                    </p>
                  )}
                </div>
              </div>

              {/* Word Limit (Minimum), Word Limit (Maximum), Mission Deadline row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <label
                    htmlFor={`word-limit-min-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Word Limit (Minimum)<span className="text-red-600">*</span>
                  </label>
                  <input
                    id={`word-limit-min-${mission.id}`}
                    name={`word-limit-min-${mission.id}`}
                    type="number"
                    value={mission.wordLimitMin}
                    placeholder="Enter minimum word limit"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) =>
                      handleInputChange(
                        mission.id,
                        'wordLimitMin',
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label
                    htmlFor={`word-limit-max-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Word Limit (Maximum)
                  </label>
                  <input
                    id={`word-limit-max-${mission.id}`}
                    name={`word-limit-max-${mission.id}`}
                    type="number"
                    value={mission.wordLimitMax}
                    placeholder="Enter Maximum word limit"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) =>
                      handleInputChange(
                        mission.id,
                        'wordLimitMax',
                        e.target.value
                      )
                    }
                  />
                </div>
                <div>
                  <label
                    htmlFor={`mission-deadline-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Mission Deadline (days)
                  </label>
                  <input
                    id={`mission-deadline-${mission.id}`}
                    name={`mission-deadline-${mission.id}`}
                    type="number"
                    value={mission.missionDeadline}
                    placeholder="Enter deadline in days"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) =>
                      handleInputChange(
                        mission.id,
                        'missionDeadline',
                        e.target.value
                      )
                    }
                  />
                </div>
              </div>

              {/* Score and Instructions */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <label
                    htmlFor={`total-score-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Score
                  </label>
                  <input
                    id={`total-score-${mission.id}`}
                    name={`total-score-${mission.id}`}
                    type="number"
                    value={mission.totalScore}
                    placeholder="Enter Score"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) =>
                      handleInputChange(
                        mission.id,
                        'totalScore',
                        e.target.value
                      )
                    }
                  />
                </div>
                <div className="md:col-span-2">
                  <label
                    htmlFor={`instruction-${mission.id}`}
                    className="block text-sm font-semibold text-black mb-2"
                  >
                    Instruction<span className="text-red-600">*</span>
                  </label>
                  <textarea
                    id={`instruction-${mission.id}`}
                    name={`instruction-${mission.id}`}
                    rows="3"
                    value={mission.instruction}
                    placeholder="Write instruction here"
                    className="w-full text-base rounded border border-gray-300 px-4 py-3 resize-none focus:outline-none focus:ring-1 focus:ring-yellow-400"
                    onChange={(e) =>
                      handleInputChange(
                        mission.id,
                        'instruction',
                        e.target.value
                      )
                    }
                  ></textarea>
                </div>
              </div>
            </div>
          ))}

          {/* <div className="flex justify-center mt-6">
            <button
              type="button"
              className="inline-flex items-center gap-2 bg-[#FFDE34] text-black text-base rounded px-6 py-3 shadow-md hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
              onClick={addMoreMission}
              disabled={isSubmitting}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add More Mission
            </button>
          </div> */}
        </form>

        <div className="flex justify-end gap-4 mt-6 mb-8">
          <button
            type="button"
            className="bg-[#D4D4D4] text-black text-base rounded px-8 py-3 shadow-md hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-400"
            onClick={handleCancel}
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="button"
            className="bg-[#FFDE34] text-black text-base rounded px-8 py-3 shadow-md hover:bg-yellow-300 focus:outline-none focus:ring-2 focus:ring-yellow-400"
            onClick={handleSave}
            disabled={isSubmitting}
          >
            {isSubmitting
              ? isEditMode
                ? 'Updating...'
                : 'Saving...'
              : isEditMode
              ? 'Update'
              : 'Save'}
          </button>
        </div>

        {showDeleteModal && (
          <DeleteModal
            isOpen={showDeleteModal}
            onClose={() => setShowDeleteModal(false)}
            data={selectedMission}
            endPoint={getQAMissionTaskDeleteEndpoint(
              auth?.user?.roles,
              taskIdToDelete
            )}
            onSuccess={refetch}
          />
        )}
      </div>
    </div>
  );
};

export default MissionQAEditor;
