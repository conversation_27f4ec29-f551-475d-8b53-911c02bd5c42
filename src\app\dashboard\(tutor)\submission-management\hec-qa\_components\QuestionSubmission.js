'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import { formatDate } from '@/utils/dateFormatter';

const QuestionSubmission = () => {
  const router = useRouter();

  // State variables
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('studentName');
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('DESC');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Fetch question submissions using useDataFetch hook
  const {
    data: submissionData,
    isLoading: loading,
    error
  } = useDataFetch({
    queryKey: ['tutor-question-submissions', currentPage, rowsPerPage, sortField, sortDirection],
    endPoint: '/tutor-qa-mission/QASubmissions',
    params: {
      page: currentPage,
      limit: rowsPerPage,
      sortBy: sortField,
      sortDirection: sortDirection
    }
  });

  // Log any errors
  if (error) {
    console.error('Error fetching question submission data:', error);
  }

  // Extract data from the API response
  const submissions = submissionData?.items?.map(item => ({
    id: item.id,
    status: item.status?.toUpperCase(),
    studentName: item.name, // You might want to fetch actual student name
    questionTitle: item.task?.title,
    submittedAt: item.createdAt,
    content: item.submissionHistory?.[0]?.content,
    wordCount: item.submissionHistory?.[0]?.wordCount
  })) || [];

  const totalItems = submissionData?.totalItems || submissionData?.totalCount || 0;
  const totalPages = submissionData?.totalPages || 1;

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (term) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    setCurrentPage(1);
  };

  // Handle sort
  const handleSort = (field, direction) => {
    setSortField(field);
    setSortDirection(direction);
  };

  // Handle view submission
  const handleViewSubmission = (submission) => {
    if (submission.status !== 'NEW') {
      router.push(`/dashboard/submission-management/hec-qa/review/${submission.id}`);
    }
  };

  // Define table columns
  const columns = [
    { field: '#', sortable: false },
    {
      field: 'studentName',
      label: 'STUDENT NAME',
      sortable: false,
      cellRenderer: (value) => (
        <div className="flex items-center">
          <div className="w-8 h-8 rounded-full bg-gray-200 mr-3 overflow-hidden">
            <div className="w-full h-full flex items-center justify-center text-gray-500">
              {value?.charAt(0)?.toUpperCase() || 'U'}
            </div>
          </div>
          <span>{value || 'Unknown'}</span>
        </div>
      )
    },
    {
      field: 'questionTitle',
      label: 'QUESTION',
      sortable: false,
      cellRenderer: (value) => <div className="truncate max-w-xs">{value || 'No title'}</div>
    },
    
    {
      field: 'status',
      label: 'STATUS',
      sortable: false,
      cellRenderer: (value) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          value === 'SUBMITTED' ? 'bg-green-100 text-green-800' :
          value === 'REVIEWED' ? 'bg-blue-100 text-blue-800' :
          value === 'NEW' ? 'bg-gray-100 text-gray-800' :
          'bg-yellow-100 text-yellow-800'
        }`}>
          {value}
        </span>
      )
    },
   
  ];

  // Define actions for table rows
  const actions = [
    {
      icon: 'heroicons-outline:eye',
      className: (row) =>
        row.status !== 'NEW'
          ? 'text-blue-600 hover:text-blue-700 cursor-pointer'
          : 'text-gray-400 cursor-not-allowed',
      onClick: (row) => handleViewSubmission(row),
      condition: (row) => row.status !== 'NEW'
    }
  ];

  // Define search filter options
  const searchFilterOptions = [
    { label: 'Student Name', value: 'studentName' },
    { label: 'Question', value: 'questionTitle' }
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      {error && (
        <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
          Error loading data: {error.message}
        </div>
      )}

      {/* Table */}
      <NewTablePage
        columns={columns}
        data={submissions}
        actions={actions}
        loading={loading}
        title='Question Submissions'
        // Pagination props
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}

        // Search and filter props
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        
        // Pass current state values
        searchTerm={searchTerm}
        searchField={searchField}
        sortField={sortField}
        sortDirection={sortDirection}

        // Pass handlers for search, filter and sort
        onSearch={handleSearch}
        onNameFilterChange={handleSearchFieldChange}
        onSort={handleSort}

        // Pass name filter options
        nameFilterOptions={searchFilterOptions}
                showCheckboxes={false}

      />
    </div>
  );
};

export default QuestionSubmission;