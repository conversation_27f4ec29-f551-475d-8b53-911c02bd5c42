/**
 * Navigation utilities for role-based routing
 */

/**
 * Get the appropriate route based on user authentication and role
 * @param {boolean} isAuth - Whether user is authenticated
 * @param {Object} user - User object containing role information
 * @param {string} fallbackRoute - Route to use for unauthenticated users (default: '/login')
 * @returns {string} The route to navigate to
 */
export const getRoleBasedRoute = (isAuth, user, fallbackRoute = '/login') => {
  // If user is authenticated and has user data
  if (isAuth && user) {
    const userRole = user.selectedRole || user.role;

    // Route based on role following middleware rules
    if (userRole === 'student') {
      return '/diary'; // Students go to diary (main area)
    } else if (userRole === 'tutor' || userRole === 'admin') {
      return '/dashboard'; // Tutors and admins go to dashboard
    } else {
      // Default fallback for authenticated users with unknown roles
      return '/diary';
    }
  } else {
    // If user is not authenticated, return fallback route
    return fallbackRoute;
  }
};

/**
 * Navigate to the appropriate route based on user role
 * @param {Function} router - Next.js router instance
 * @param {boolean} isAuth - Whether user is authenticated
 * @param {Object} user - User object containing role information
 * @param {string} fallbackRoute - Route to use for unauthenticated users (default: '/login')
 */
export const navigateByRole = (router, isAuth, user, fallbackRoute = '/login') => {
  const route = getRoleBasedRoute(isAuth, user, fallbackRoute);
  router.push(route);
};

/**
 * Hook to get role-based navigation function
 * @param {Function} router - Next.js router instance
 * @param {boolean} isAuth - Whether user is authenticated
 * @param {Object} user - User object containing role information
 * @returns {Function} Navigation function that takes fallbackRoute as parameter
 */
export const useRoleBasedNavigation = (router, isAuth, user) => {
  return (fallbackRoute = '/login') => {
    navigateByRole(router, isAuth, user, fallbackRoute);
  };
};
