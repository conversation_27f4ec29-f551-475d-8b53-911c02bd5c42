'use client';

import NewTablePage from "@/components/form/NewTablePage";
import TutorViewModal from "./view/page"; // Import the TutorViewModal
import useDataFetch from "@/hooks/useDataFetch";
import { useRouter } from 'next/navigation';
import React, { useState, useCallback } from 'react';
import { debounce } from 'lodash';

// Helper function to map partial gender inputs to valid API values
const mapGenderInput = (input) => {
  if (!input) return '';
  
  const lowerInput = input.toLowerCase().trim();
  
  // Match "male" variations
  if (['m', 'ma', 'mal', 'male'].includes(lowerInput)) {
    return 'male';
  }
  
  // Match "female" variations
  if (['f', 'fe', 'fem', 'fema', 'femal', 'female'].includes(lowerInput)) {
    return 'female';
  }
  
  // Match "other" variations
  if (['o', 'ot', 'oth', 'othe', 'other'].includes(lowerInput)) {
    return 'other';
  }
  
  return ''; // Return empty if no match
};

const TutorList = () => {
  const router = useRouter();
  
  // State for view modal
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedTutor, setSelectedTutor] = useState(null);
  
   // Handle back button click
   const handleBackClick = () => {
    router.back();
  };
  
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(100);
  
  // State for search and sorting
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('userId');
  const [sortField, setSortField] = useState('userId');
  const [sortDirection, setSortDirection] = useState('DESC');
  
  // Create debounced search function
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedSearch = useCallback(
    debounce((term) => {
      setSearchTerm(term);
      setCurrentPage(1); // Reset to first page on new search
    }, 300),
    []
  );
  
  // Prepare query parameters for API request
  const prepareQueryParams = useCallback(() => {
    const params = {
      page: currentPage,
      limit: rowsPerPage,
      sortBy: sortField,
      sortDirection: sortDirection
    };
    
    // Only add search term if it's not empty
    if (searchTerm && searchTerm.trim() !== '') {
      // Special handling for gender field
      if (searchField === 'gender') {
        const mappedGender = mapGenderInput(searchTerm);
        if (mappedGender) {
          params[searchField] = mappedGender;
        }
        // If no valid gender match, don't add the parameter
      } 
      // For userId, handle spaces specially
      else if (searchField === 'userId') {
        const firstWord = searchTerm.split(' ')[0];
        params[searchField] = firstWord;
      } 
      // For other fields, use the full search term
      else {
        params[searchField] = searchTerm.trim();
      }
    }
    
    return params;
  }, [currentPage, rowsPerPage, sortField, sortDirection, searchField, searchTerm]);
  
  // Get the current query parameters
  const queryParams = prepareQueryParams();
  
  // Handler for search input changes
  const handleSearch = (term) => {
    console.log('Search term:', term); // Debug log
    debouncedSearch(term);
  };
  
  // Fetch tutors data using the custom hook
  const {
    data: response,
    isLoading,
    error
  } = useDataFetch({
    queryKey: ['users/tutor', queryParams],
    endPoint: '/users/tutor',
    params: queryParams
  });
  
  // Function to get total count handling different response structures
  const getTotalCount = () => {
    if (!response) return 0;
    
    // First try to get from meta.totalItems (current structure)
    if (response.meta?.totalItems !== undefined) {
      return response.meta.totalItems;
    }
    
    // Fallback to response.totalCount if available
    if (response.totalCount !== undefined) {
      return response.totalCount;
    }
    
    // Last resort: count the items in the array
    return (response.items || []).length;
  };
  
  // Client-side filtering for multi-word searches and exact gender matching
  const filteredTutors = React.useMemo(() => {
    const tutors = response?.items || [];
    
    // Apply filters based on search conditions
    return tutors.filter(tutor => {
      // Handle multi-word name searches
      if (searchTerm && searchTerm.includes(' ') && searchField === 'userId') {
        if (!tutor.name) return false;
        
        // Case-insensitive matching for full name
        const tutorName = tutor.name.toLowerCase();
        const searchTermLower = searchTerm.toLowerCase();
        
        return tutorName.includes(searchTermLower);
      }
      
      // Handle gender exact matching - we use our smart mapping function
      if (searchField === 'gender' && searchTerm) {
        const mappedGender = mapGenderInput(searchTerm);
        const tutorGender = (tutor.gender || '').toLowerCase().trim();
        
        // If we have a valid mapping, match exactly
        if (mappedGender) {
          return tutorGender === mappedGender;
        }
      }
      
      // Default: include the tutor in results
      return true;
    });
  }, [response, searchTerm, searchField]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search field change
  const handleSearchFieldChange = (field) => {
    setSearchField(field);
    // Clear search when field changes to avoid confusion
    setSearchTerm('');
  };

  // Handle sort change
  const handleSort = (field) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      // Set new field and default to ASC
      setSortField(field);
      setSortDirection('ASC');
    }
  };

  // Handle view button click
  const handleViewTutor = (tutorData) => {
    setSelectedTutor(tutorData);
    setIsViewModalOpen(true);
  };

  // Handle modal close
  const handleCloseModal = () => {
    setIsViewModalOpen(false);
    setSelectedTutor(null);
  };

  // Extract metadata from response using the new getTotalCount function
  const totalItems = getTotalCount();
  const totalPages = response?.meta?.totalPages || Math.ceil(totalItems / rowsPerPage) || 1;

  // Navigation function for the "Add Tutor" button
  const handleCreateTutor = () => {
    router.push('/dashboard/member-management/add-tutor');
  };

  // Define name filter options
  const nameFilterOptions = [
    { label: 'Tutor Name', value: 'userId' },
    { label: 'Email', value: 'email' },
    { label: 'Phone Number', value: 'phoneNumber' },
    { label: 'Gender', value: 'gender' }
  ];
  
  const UserAvatar = ({ name }) => {
    return (
      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-amber-100 text-amber-800">
        <svg 
          className="h-5 w-5" 
          xmlns="http://www.w3.org/2000/svg" 
          viewBox="0 0 24 24" 
          fill="currentColor"
        >
          <path 
            fillRule="evenodd" 
            d="M7.5 6a4.5 4.5 0 119 0 4.5 4.5 0 01-9 0zM3.751 20.105a8.25 8.25 0 0116.498 0 .75.75 0 01-.437.695A18.683 18.683 0 0112 22.5c-2.786 0-5.433-.608-7.812-1.7a.75.75 0 01-.437-.695z" 
            clipRule="evenodd" 
          />
        </svg>
      </div>
    );
  };

  // Define columns for the table with sorting
  const columns = [
    {
      label: 'TUTOR NAME',
      field: 'name',
      cellRenderer: (value, row) => (
        <div className="flex items-center space-x-2">
          <UserAvatar name={value} />
          <span className="font-medium text-gray-900">{value}</span>
        </div>
      )
    },

    {
      label: ' TUTOR ID',
      field : 'userId'
    },
    {
      label: 'EMAIL ADDRESS',
      field: 'email',
    },
    {
      label: 'PHONE NUMBER',
      field: 'phoneNumber',
    },
    {
      label: 'GENDER',
      field: 'gender',
    }
  ];

    const handleMessageTutor = (tutorData) => {
  console.log('Navigating to chat for tutor:', tutorData);
  router.push('/chat-app');
};

  // Define action buttons - updated to use view instead of message
  const actions = [

     {
      name: 'message',
      icon: 'material-symbols:chat',
      className: 'text-green-600',
       onClick: (row) => handleMessageTutor(row), 
    },
    {
      name: 'view',
      icon: 'material-symbols:visibility',
      className: 'text-blue-600',
      onClick: (row) => handleViewTutor(row),
    },
  ];

  // Display error message if API call fails
  if (error) {
    return (
      <div className="p-4 text-red-600">
        Error loading tutors: {error.message}
      </div>
    );
  }

  return (
    <div className="w-full px-4">
      <div className="overflow-auto max-h-[80vh]">
        <NewTablePage
          title="Tutors List"
          createButton="Add Tutor"
          columns={columns}
          actions={actions}
          data={filteredTutors}
          loading={isLoading}
          showCreateButton={true}
          openCreateModal={handleCreateTutor}
          onBack={handleBackClick}
          showCheckboxes={false}
          
          // Pagination props
          currentPage={currentPage}
          totalPages={totalPages}
          changePage={handlePageChange}
          totalItems={totalItems}
          rowsPerPage={rowsPerPage}
          setRowsPerPage={setRowsPerPage}
          
          // Search and filter props
          showSearch={true}
          showNameFilter={true}
          showSortFilter={true}
          
          // Pass current state values
          searchTerm={searchTerm}
          searchField={searchField}
          sortField={sortField}
          sortDirection={sortDirection}
          
          // Pass handlers for search, filter and sort
          onSearch={handleSearch}
          onNameFilterChange={handleSearchFieldChange}
          onSort={handleSort}
          
          // Pass name filter options
          nameFilterOptions={nameFilterOptions}
        />
      </div>
      
      {/* Tutor View Modal */}
      <TutorViewModal
        isOpen={isViewModalOpen}
        onClose={handleCloseModal}
        tutorData={selectedTutor}
      />
    </div>
  );
};

export default TutorList;