'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useDispatch, useSelector } from 'react-redux';
import { useRouter, useSearchParams } from 'next/navigation';

import { loginSuccess, setGoBackStep } from '@/store/features/authSlice';
import api from '@/lib/api';
import { Formik, Form, Field } from 'formik';
import * as Yup from 'yup';
import FormInput from '@/components/form/FormInput';
import { Icon } from '@iconify/react';
import Link from 'next/link';

const LoginSchema = Yup.object().shape({
  userId: Yup.string().required('Please provide your user id'),
  password: Yup.string()
    .required('Please enter your password')
    .min(8, 'Password must be at least 8 characters')
});

const roles = [
  {
    id: 'admin',
    title: 'Admin',
    image: '/assets/images/auth/role-admin.png',
  },
  {
    id: 'tutor',
    title: 'Tutor',
    image: '/assets/images/auth/role-mentor.png',
  },
  {
    id: 'student',
    title: 'Student',
    image: '/assets/images/auth/role-user.png',
  },
];

export default function LoginPage() {
  const savedUserId = localStorage.getItem('userId');
  const savedPassword = localStorage.getItem('password');
  const savedSelectedRole = localStorage.getItem('selectedRole');

  const [selectedRole, setSelectedRole] = useState(savedSelectedRole || null);
  // const [showForm, setShowForm] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const dispatch = useDispatch();
  const router = useRouter();
  const currentPath = useSelector((state) => state.auth.goBackStep);
  const searchParams = useSearchParams();
  const returnTo = searchParams.get('returnTo');

  // Reset path when component mounts
  useEffect(() => {
    dispatch(
      setGoBackStep(savedSelectedRole ? 'login-role/login-form' : 'login-role')
    );
  }, [dispatch]);

  const handleRoleSelect = (roleId) => {
    setSelectedRole(roleId);
  };

  const handleNext = () => {
    dispatch(setGoBackStep('login-role/login-form'));
  };

  const handleLogin = async (values, { setSubmitting, setErrors }) => {
    // console.log(values);
    try {
      setSubmitting(true);

      const response = await api.post('/auth/login', values);
      // const profileImg = await api.get('/users/profile/picture');
      console.log(response);

      if (response?.data?.access_token) {
        dispatch(
          loginSuccess({
            user: response?.data?.user,
            token: response?.data?.access_token,
            token_expires: response?.data?.token_expires,
          })
        );

        if (values?.rememberMe) {
          localStorage.setItem('userId', values.userId);
          localStorage.setItem('password', values.password);
          localStorage.setItem('selectedRole', values.selectedRole);
        } else {
          localStorage.removeItem('userId');
          localStorage.removeItem('password');
          localStorage.removeItem('selectedRole');
        }

        // await new Promise((resolve) => setTimeout(resolve, 100));
        if (returnTo) {
          router.push(returnTo);
          return;
        }
        if (response?.data?.user?.activePlan === null && response?.data?.user?.selectedRole === 'student') {
          router.push('/pricing-plans');
          return;
        } else {
          if(response?.data?.user?.selectedRole === 'student' && response?.data?.user?.activePlan){
            router.push('/diary');
          } else if(response?.data?.user?.selectedRole === 'tutor' || response?.data?.user?.selectedRole === 'admin') {
            router.push('/dashboard');
          }
        }
      }
    } catch (error) {
      console.log(error);
      setErrors({
        general: error?.response?.data?.message || 'Login failed!',
      });
    }
    setSubmitting(false);
  };
  return (
    <div className="w-full max-w-2xl mx-auto p-4">
      {currentPath === 'login-role' ? (
        <>
          <div className="text-center mb-8 text-start">
            <h2 className="text-4xl font-semibold mb-4">Select user role</h2>
            <h2 className="text-2xl font-[500] mb-4">
              Select user role for Log in
            </h2>
            <p className="text-gray-600">
              When logging in, users must select their role to access the
              appropriate features and permissions within the system.{' '}
              <span className="font-semibold">Admin</span> ,
              <span className="font-semibold"> Tutor</span>, and{' '}
              <span className="font-semibold"> Student</span> , each with different
              levels of access. Selecting the correct role ensures a
              personalized and secure experience
            </p>
          </div>

          <div className="grid grid-cols-3 max-sm:gap-3 gap-6 mb-8">
            {roles.map((role) => (
              <div
                key={role.id}
                onClick={() => handleRoleSelect(role.id)}
                className={`relative rounded-lg p-4 cursor-pointer transition-all duration-300 hover:shadow-lg
                  ${
                    selectedRole === role.id
                      ? 'border-2 border-green-500 shadow-lg'
                      : 'border-2 border-gray-200'
                  }`}
              >
                <div className="flex flex-col items-center">
                  <div className="w-14 h-14 xl:w-24 xl:h-24 relative mb-2 flex items-center">
                    <Image
                      src={role.image}
                      alt={role.title}
                      height={300}
                      width={300}
                      className="object-contain"
                    />
                  </div>
                  <h3 className="text-xl max-sm:text-base font-semibold mb-2">
                    {role.title}
                  </h3>
                </div>
                {selectedRole === role.id && (
                  <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
                    <Icon
                      icon="mdi:check-circle"
                      className="text-green-500 w-6 h-6 bg-white rounded-full"
                    />
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-center">
            <button
              onClick={handleNext}
              disabled={!selectedRole}
              className={`px-5 py-3 w-full rounded-lg text-xl font-[500] transition-all duration-300
                ${
                  selectedRole
                    ? 'bg-[#FFDE34] text-gray-700 hover:bg-yellow-400'
                    : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                }`}
            >
              Next{' '}
              <Icon
                icon="ep:right"
                width="18"
                height="18"
                className="inline-block mb-0.5 ml-1"
              />
            </button>
          </div>

          <div className="text-center text-sm text-gray-600 pt-4">
            Don't have an account?
            <Link
              href="/register"
              className="text-yellow-500 hover:text-yellow-600 ml-1"
            >
              Create Account
            </Link>
          </div>
        </>
      ) : (
        <div className="flex flex-col items-center">
          <div className="w-full max-w-md bg-white rounded-lg">
            <div className="px-4 py-6 pb-0 space-y-4">
              <h1 className="text-xl font-bold text-gray-900">
                HELLO ENGLISH COACHING - HEC
              </h1>

              {/* <h2 className="text-lg font-semibold text-gray-800">LOGIN</h2> */}

              <p className="text-gray-600">Login Into Your Account</p>

              <p className="text-gray-500">
                Now you're going to login as{' '}
                {selectedRole === 'admin' ? 'an' : 'a'}{' '}
                <span className="font-semibold">
                  {selectedRole?.charAt(0)?.toUpperCase() +
                    selectedRole?.slice(1)}
                </span>
                .
              </p>

              <Formik
                initialValues={{
                  userId: savedUserId || '',
                  // email: '',
                  password: savedPassword || '',
                  rememberMe: false,
                  selectedRole: selectedRole,
                }}
                validationSchema={LoginSchema}
                onSubmit={handleLogin}
              >
                {({ isSubmitting, errors, touched, values }) => (
                  <Form className="space-y-2">
                    {errors.general && (
                      <div className="text-red-500 text-sm text-center">
                        {errors.general}
                      </div>
                    )}

                    <div className="">
                      <div className="w-full">
                        <FormInput
                          label="ID"
                          name="userId"
                          type="text"
                          placeholder="Enter your user id"
                          required
                        />
                      </div>
                      <div className="flex justify-end mt-1">
                        <Link
                          href="/forgot-userid"
                          className="text-sm text-red-500 hover:text-red-600"
                        >
                          Forgot ID?
                        </Link>
                      </div>
                    </div>
                    {/* <FormInput
                      label="Email Address"
                      name="email"
                      type="email"
                      placeholder="Enter email address"
                      required
                    /> */}

                    <div>
                      <div className="relative">
                        <FormInput
                          label="Password"
                          name="password"
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter password"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-[42px] transform text-gray-500"
                        >
                          <Icon
                            icon={
                              showPassword
                                ? 'mdi:eye-off'
                                : 'mdi:eye'
                            }
                            width="16"
                            height="16"
                          />
                        </button>
                      </div>
                      <div className="flex justify-end mt-1">
                        <Link
                          href="/forgot-password"
                          className="text-sm text-red-500 hover:text-red-600"
                        >
                          Forgot Password?
                        </Link>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <label className="flex items-center cursor-pointer">
                        <Field
                          type="checkbox"
                          name="rememberMe"
                          className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-sm text-gray-600">
                          Remember Me
                        </span>
                      </label>
                      {/* <Link
                        href="/forgot-password"
                        className="text-sm text-red-500 hover:text-red-600"
                      >
                        Forgot Password?
                      </Link> */}
                    </div>

                    <button
                      type="submit"
                      disabled={
                        isSubmitting || !values.userId || !values.password
                      }
                      className={`w-full py-2 px-4 rounded-md transition-colors ${
                        isSubmitting || !values.userId || !values.password
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : 'bg-yellow-300 text-gray-800 hover:bg-yellow-400'
                      }`}
                    >
                      {isSubmitting ? 'Logging in...' : 'Login'}
                    </button>
                  </Form>
                )}
              </Formik>

              <div className="text-center text-sm text-gray-600">
                Don't have an account?
                <Link
                  href="/register"
                  className="text-yellow-500 hover:text-yellow-600 ml-1"
                >
                  Create Account
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
