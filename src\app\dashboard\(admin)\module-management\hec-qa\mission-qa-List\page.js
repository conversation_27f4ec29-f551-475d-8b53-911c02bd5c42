'use client';

import React, { useState, useEffect } from 'react';
import api from '@/lib/api';
import NewTablePage from '@/components/form/NewTablePage';
import DeleteModal from '@/components/form/modal/DeleteModal';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { toast } from 'react-toastify';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { Icon } from '@iconify/react';
import { useSelector } from 'react-redux';

const QAMissionList = () => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const auth = useSelector((state) => state.auth);
  const getQAMissionListEndpoint = (roles) =>
    roles?.includes('tutor') ? '/tutor/qa-mission/list' : '/admin/qa-mission/list';
  const getQAMissionDetailEndpoint = (roles, id) =>
    roles?.includes('tutor') ? `/tutor/qa-mission/${id}` : `/admin/qa-mission/${id}`;
  // State variables
  const [missions, setMissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchField, setSearchField] = useState('title'); // Can be 'title' or 'week'
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState('ASC');
  const [activeTab, setActiveTab] = useState(0); // 0 for Weekly, 1 for Monthly
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  
  // Add new state for selected mission data
  const [selectedMission, setSelectedMission] = useState(null);
  const [fetchingMission, setFetchingMission] = useState(false);
  
  // Add state for week or month number - initially empty
  const [weekOrMonth, setWeekOrMonth] = useState('');
  
  // Add state for delete confirmation modal - Updated to use DeleteModal
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Tabs content
  const tabs = [
    { name: 'Weekly Q&A', frequency: 'weekly' },
    { name: 'Monthly Q&A', frequency: 'monthly' }
  ];

  // Generate week options (1-52) for dropdown
  const weekOptions = Array.from({ length: 52 }, (_, i) => ({
    label: `Week ${i + 1}`,
    value: (i + 1).toString(),
  }));

  // Generate month options (1-12) for dropdown
  const monthOptions = Array.from({ length: 12 }, (_, i) => ({
    label: `Month ${i + 1}`,
    value: (i + 1).toString(),
  }));

  // Function to encode mission ID - makes URL shorter
  const encodeMissionId = (id) => {
    // Convert to base64 and remove padding
    return Buffer.from(id.toString()).toString('base64').replace(/=/g, '');
  };

  // Dynamic name filter options based on active tab
  const getNameFilterOptions = () => {
    if (activeTab === 0) { // Weekly tab
      return [
        { label: 'All', value: '' }, // Add "All" option
        { label: 'Title', value: 'title' },
        { label: 'Week', value: 'week' }, // UI label is 'Week' but will use 'weekOrMonth' parameter
      ];
    } else { // Monthly tab
      return [
        { label: 'All', value: '' }, // Add "All" option
        { label: 'Title', value: 'title' },
        { label: 'Month', value: 'month' }, // UI label is 'Month' but will use 'weekOrMonth' parameter
      ];
    }
  };

  // Table columns definition - updated for new response structure
  const columns = activeTab === 0 
    ? [
        
        { 
          label: 'MISSION Q & A TITLE', 
          field: 'sequenceNumber',
          sortable: false,
          cellRenderer: (value, row) => (
            <div className="flex items-center">
              <span className="text-sm font-medium">Week - {value}</span>
            </div>
          )
        },
        { label: 'TOTAL TASKS', field: 'totalTasks' },
       
      ]
    : [
       
        {
          label: ' MISSION Q & A TITLE',
          field: 'sequenceNumber',
          sortable: false,
          cellRenderer: (value, row) => {
            // Map the sequence number to month name
            const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
            
            // Adjust for 1-based indexing (sequence starts at 1, array index starts at 0)
            const monthName = monthNames[(value - 1) % 12];
            
            return (
              <div className="flex items-center">
                <span className="text-sm font-medium">{monthName}</span>
              </div>
            );
          }
        },
        { label: 'TOTAL TASKS', field: 'totalTasks' },
       
      ];

  // Function to fetch a single mission by ID
  const fetchMissionById = async (missionId) => {
  try {
    setFetchingMission(true);
    const endpoint = getQAMissionDetailEndpoint(auth?.user?.roles, missionId);
    const response = await api.get(endpoint);
    if (response && response.success) {
      setSelectedMission(response.data);
      return response.data;
    }
    return null;
  } catch {
    return null;
  } finally {
    setFetchingMission(false);
  }
};


  // Handle view action - navigate to view page
  const handleViewMission = (mission) => {
    const missionId = mission.id;
    console.log('Viewing mission:', missionId);
    router.push(`/dashboard/module-management/hec-qa/mission-qa-List/view/${missionId}`);
  };

  // Handle edit action - navigate to edit form with mission ID
  const handleEditMission = async (mission) => {
    // Use the mission ID directly from the new response structure
    const missionId = mission.id;
    
    console.log('Fetching mission details for editing:', missionId);
    const missionDetails = await fetchMissionById(missionId);
    
    if (missionDetails) {
      // Make sure all necessary data is preserved, including totalScore
      // If tasks exist, ensure each task has the totalScore field
      if (missionDetails.tasks && missionDetails.tasks.length > 0) {
        missionDetails.tasks = missionDetails.tasks.map(task => {
          // Preserve totalScore if it exists, otherwise set a default value
          if (!task.hasOwnProperty('totalScore')) {
            console.log('Adding missing score field to task:', task.title);
            task.totalScore = task.totalScore || 10; // Default value of 10 if missing
          }
          return task;
        });
      }
      
      // Store mission details in sessionStorage to maintain state between page navigations
      sessionStorage.setItem('editMissionData', JSON.stringify(missionDetails));
      
      // Navigate to the create/edit form with the mission ID
      router.push(`/dashboard/module-management/hec-qa/create-mission-qa?id=${missionId}`);
    } else {
      // Show an error notification if mission data couldn't be fetched
      toast.error('Could not retrieve mission data for editing');
    }
  };

  // Table actions definition - Updated with working view function
  const actions = [
    {
      icon: 'heroicons-outline:pencil',
      className: 'text-gray-600 hover:text-blue-600',
      onClick: handleEditMission
    },
    {
      icon: 'material-symbols:visibility',
      className: 'text-blue-600 hover:text-blue-600',
      onClick: handleViewMission
    },
    {
      name: 'delete',
      icon: 'heroicons-outline:trash',
      className: 'text-red-600 hover:text-red-700',
      onClick: (row) => {
        setSelectedMission(row);
        setShowDeleteModal(true);
      }
    }
  ];

  // Create a React Query key for missions list
  const getMissionsQueryKey = () => [
    'missionsList',
    activeTab,
    currentPage,
    rowsPerPage,
    sortDirection,
    searchField,
    searchTerm,
    weekOrMonth
  ];

  // Use React Query for missions list
  const { refetch } = useQuery({
    queryKey: getMissionsQueryKey(),
    queryFn: fetchMissions,
    enabled: false, // We'll call fetchMissions manually
  });

  // Fetch data from API - updated for new response structure
  async function fetchMissions() {
    try {
      setLoading(true);
      
      // Base params required by the API
      const params = {
        page: currentPage,
        limit: rowsPerPage,
        timeFrequency: tabs[activeTab].frequency,
        sortDirection: sortDirection
      };
      
      // Set sortBy based on the searchField for proper column sorting
      if (searchField) {
        params.sortBy = searchField;
      }
      
      // Add search parameters based on searchField type
      if (searchField === 'title' && searchTerm) {
        params.title = searchTerm;
      } 
      else if ((searchField === 'week' || searchField === 'month') && weekOrMonth) {
        // Always use 'weekOrMonth' as the parameter name for both weekly and monthly searches
        params.weekOrMonth = weekOrMonth;
      }
      
      // Log the params being sent to the API for debugging
      console.log('API request params:', params);
      // Determine endpoint based on user role
           const endpoint = getQAMissionListEndpoint(auth?.user?.roles);

      const response = await api.get(endpoint, { params });

      if (response && response.success) {
        // Use the mission data directly from the new response structure
        const missionItems = response.data.items || [];
        
        // Sort missions by sequenceNumber if necessary
        const sortedMissions = [...missionItems].sort((a, b) => 
          (a.sequenceNumber || 0) - (b.sequenceNumber || 0)
        );
        
        // Format missions for display - no need to expand tasks since they're not in the response
        const formattedMissions = sortedMissions.map((mission) => ({
          id: mission.id,
          sequenceNumber: mission.sequenceNumber || 0,
          timeFrequency: mission.timeFrequency,
          totalTasks: mission.totalTasks || 0,
          isActive: mission.isActive,
          weekId: mission.weekId,
          monthId: mission.monthId
        }));
        
        setMissions(formattedMissions);
        
        // Use correct pagination values from the API response
        setTotalItems(response.data.totalItems || 0);
        setTotalPages(response.data.totalPages || 1);
      } else {
        console.error('Failed to fetch missions:', response?.message || 'Unknown error');
      }
    } catch (error) {
      console.error('Error fetching missions:', error);
    } finally {
      setLoading(false);
    }
  }

  useEffect(() => {
    fetchMissions();
  }, [activeTab, currentPage, rowsPerPage, sortDirection, searchTerm, searchField, weekOrMonth]);

  // Reset page and search when changing tabs
  useEffect(() => {
    setCurrentPage(1);
    setSearchTerm('');
    setWeekOrMonth('');
    setSearchField(''); // Reset to empty to show all missions
  }, [activeTab]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page on new search
    
    // Clear weekOrMonth when searching by title
    if (searchField === 'title') {
      setWeekOrMonth('');
    }
  };

  // Handle week/month selection
  const handleWeekOrMonthChange = (value) => {
    setWeekOrMonth(value);
    setCurrentPage(1); // Reset to first page on filter change
    
    // Clear text search when using week/month filter
    if (searchField === 'week' || searchField === 'month') {
      setSearchTerm('');
      // Log for debugging the request format
      console.log('Setting weekOrMonth parameter to:', value);
    }
  };

  // Handle name filter change
  const handleNameFilterChange = (field) => {
    setSearchField(field);
    setCurrentPage(1); // Reset to first page on filter change
    
    // Clear search values when changing filter type
    setSearchTerm('');
    setWeekOrMonth('');
    
    // Update sortBy to match the searchField for proper column sorting if field is not empty
    if (field) {
      setSortField(field);
    }
  };

  // Handle sort change
  const handleSort = (field) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'ASC' ? 'DESC' : 'ASC');
    } else {
      // New field, default to ascending
      setSortField(field);
      setSortDirection('ASC');
    }
    setCurrentPage(1); // Reset to first page on sort change
  };

  // Tab switching handler
  const handleTabChange = (index) => {
    setActiveTab(index);
  };

  // Create a new mission handler
  const handleCreateMission = () => {
    // Clear any previous edit data
    sessionStorage.removeItem('editMissionData');
    
    // Navigate to create mission page
    router.push('/dashboard/module-management/hec-qa/create-mission-qa');
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Q&A Mission List</h1>
      </div>
      
      {/* Tab buttons */}
      <div className="flex border-b border-gray-200 mb-6">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={`py-3 px-6 font-medium text-sm focus:outline-none ${
              activeTab === index 
                ? 'text-black border-b-2 border-yellow-500 hover:bg-[#FEFCE8]' 
                : 'text-gray-500 hover:text-gray-700'
            }`}
            onClick={() => handleTabChange(index)}
          >
            {tab.name}
          </button>
        ))}
      </div>
      
      {/* Search and filter section */}
      <div className="flex flex-wrap gap-4 mb-6">
        {/* Name filter selector - fixed width */}
        <div className="w-48">
          <label className="block text-sm font-medium text-gray-700 mb-1">Search by</label>
          <select
            value={searchField}
            onChange={(e) => handleNameFilterChange(e.target.value)}
            className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All</option>
            {getNameFilterOptions().filter(option => option.value !== '').map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        
        {/* Dynamic search input based on selected search field */}
        {searchField === 'title' ? (
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">Search mission title</label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              placeholder="Enter mission title..."
              className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        ) : searchField === 'week' ? (
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select week number
            </label>
            <select
              value={weekOrMonth}
              onChange={(e) => handleWeekOrMonthChange(e.target.value)}
              className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All weeks</option>
              {weekOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ) : searchField === 'month' ? (
          <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Select month number
            </label>
            <select
              value={weekOrMonth}
              onChange={(e) => handleWeekOrMonthChange(e.target.value)}
              className="w-full h-10 border border-gray-300 rounded-md px-3 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All months</option>
              {monthOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ) 
        : (
          // When "All" is selected, show nothing or a message
          <div className="flex-1 flex items-end">
            <p className="w-full h-10 border border-gray-300 rounded-md px-3 flex items-center focus:ring-blue-500 focus:border-blue-500">
              Showing all missions
            </p>
          </div>
        )}
      </div>
      
      {/* Show loading indicator when fetching individual mission */}
      {fetchingMission && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg shadow-lg">
            <p className="text-gray-700">Loading mission data...</p>
          </div>
        </div>
      )}
      
      {/* Table component */}
      <NewTablePage
        title=""
        createButton="Create Mission"
        showCreateButton={false}
        columns={columns}
        actions={actions}
        data={missions}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        changePage={handlePageChange}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        // Search and filter props
        showSearch={false} // We're handling search manually
        showNameFilter={false} // We're handling filters manually
        showSortFilter={false}
        onSort={handleSort}
        sortField={sortField}
        sortDirection={sortDirection}
        showCheckboxes={false}
      />
      
      {/* DeleteModal Integration */}
      {showDeleteModal && (
        <DeleteModal
          isOpen={showDeleteModal}
          onClose={() => setShowDeleteModal(false)}
          data={selectedMission}
  endPoint={getQAMissionDetailEndpoint(auth?.user?.roles, selectedMission?.id)}
          onSuccess={refetch}
        />
      )}
      
      {missions.length === 0 && !loading && (
        <div className="text-center py-8 text-gray-500">
          No missions found. {searchTerm || weekOrMonth ? 'Try adjusting your search criteria.' : ''}
        </div>
      )}
    </div>
  );
};

export default QAMissionList;