'use client';
import Modal from '@/components/Modal';
import useDataFetch from '@/hooks/useDataFetch';
import Image from 'next/image';
import React, { useState } from 'react';
import api from '@/lib/api';
import { useRouter } from 'next/navigation';

const SkinSelectionModal = ({ isOpen, onClose }) => {
    const router = useRouter();
  const { data, isLoading, error } = useDataFetch({
    queryKey: ['novel-skins'],
    endPoint: `/student/novel/skins`,
  });
  const skins = data?.items || [];
  const [selectedSkinId, setSelectedSkinId] = useState(null);
  const [applying, setApplying] = useState(false);

  const handleApply = async () => {
    if (!selectedSkinId) return;

    try {
      setApplying(true);
      await api.put(`/student/novel/skins/${selectedSkinId}/set-as-default`);
      router.push(`/novel/${selectedSkinId}`);
      onClose(); // Optionally close modal after apply
    } catch (err) {
      console.error('Failed to apply skin:', err);
    } finally {
      setApplying(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Select Skin" width="4xl">
      <div className="min-h-40">
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-yellow-500"></div>
          </div>
        ) : error ? (
          <div className="text-center text-red-500 py-10">
            Failed to load skins.
          </div>
        ) : (
          <>
            {/* Skins Grid */}
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-bold">Please select a default skin first. </h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mb-6">
              {skins.map((skin) => (
                <div
                  key={skin.id}
                  onClick={() => setSelectedSkinId(skin.id)}
                  className={`relative border rounded-lg overflow-hidden cursor-pointer transition-all duration-200 ${
                    selectedSkinId === skin.id
                      ? 'border-yellow-500 shadow-md border-2'
                      : 'border-gray-100 hover:shadow-lg'
                  }`}
                >
                  {/* Selection indicator */}
                  {selectedSkinId === skin.id && (
                    <div className="absolute top-2 right-2 w-6 h-6 rounded-full bg-yellow-500 flex items-center justify-center z-20">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  )}
                  <div className="h-40 flex items-center justify-center bg-white relative">
                    <div className="absolute inset-1 border-2 border-yellow-200 rounded-lg"></div>
                    <div className="relative z-10 w-full h-full flex items-center justify-center p-2">
                      <Image
                        src={skin.previewImagePath || '/assets/images/placeholder.png'}
                        alt={skin.name}
                        width={240}
                        height={180}
                        className="object-contain max-w-full max-h-full"
                      />
                      <div className="absolute bottom-0 left-0 right-0 bg-white bg-opacity-80 p-2 text-center">
                        <p className="text-sm font-medium text-gray-800">
                          {skin.name}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Apply Button */}
            <div className="flex justify-end">
              <button
                onClick={handleApply}
                disabled={!selectedSkinId || applying}
                className={`px-6 py-2 rounded-xl text-white font-medium transition-all border-2 border-white ${
                  selectedSkinId
                    ? 'bg-gradient-to-b from-yellow-400 via-yellow-500 to-amber-600 hover:from-yellow-500 hover:to-amber-700'
                    : 'bg-gray-400 cursor-not-allowed'
                }`}
              >
                {applying ? 'Applying...' : 'Apply'}
              </button>
            </div>
          </>
        )}
      </div>
    </Modal>
  );
};

export default SkinSelectionModal;
