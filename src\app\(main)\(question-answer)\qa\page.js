'use client';
import Button, { ButtonIcon } from '@/components/Button';
import <PERSON><PERSON>iewer from '@/components/EditorViewer';
import DetailsModal from '@/components/form/modal/MissionConfirmationModal';
import TinyMceEditor from '@/components/form/TinyMceEditor';
import GoBack from '@/components/shared/GoBack';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';
import { Editor } from '@tinymce/tinymce-react';
import { Form, Formik } from 'formik';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';

const HecLatestAnswer = () => {
  const router = useRouter();
  const editorRef = useRef(null);
  const [value, setValue] = useState('');
  const [wordCount, setWordCount] = useState(0);
  const { user } = useSelector((state) => state.auth);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [autoSaveTimeout, setAutoSaveTimeout] = useState(null);

  const { data, refetch } = useDataFetch({
    queryKey: ['hec-answer'],
    endPoint: `/qa/latest-assignments/${user?.id}`,
  });

  const showSubmission = data?.submissions?.length > 0 && !isSubmitted;
  const showFeedback =
    data?.submissions?.length > 0 && data?.submission?.feedback;

  // Count words in HTML content
  const countWords = (html) => {
    if (!html) return 0;
    // Remove HTML tags
    const text = html?.replace(/<[^>]*>/g, ' ');
    // Remove entities
    const cleanText = text.replace(/&nbsp;|&amp;|&lt;|&gt;|&quot;|&#39;/g, ' ');
    // Remove extra spaces and split by whitespace
    const words = cleanText
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    return words.length;
  };

  const handleSubmit = async (values) => {
    try {
      console.log('values:', values);
      // Check minimum word count
      const wordCount = countWords(values?.answer?.answer || values?.answer);
      const minimumWords = data?.task?.wordLimitMinimum || 0;
      const maximumWords = data?.task?.wordLimitMaximum || Infinity;

      if (wordCount < minimumWords || wordCount > maximumWords) {
        toast.message(
          `Your answer must contain between ${minimumWords} and ${maximumWords} words. Current word count: ${wordCount}`
        );
        return;
      }

      const response = await api.post(`/qa/submissions/{id}/submit`, {
        answer: values.answer?.answer || values.answer,
        setSequence: data?.setSequence,
        wordCount: wordCount,
      });
      console.log(response);
      refetch();
      setIsSubmitted(true);
    } catch (error) {
      console.log(error);
    }
  };

  const handleUpdate = async (values) => {
    try {
      // Ensure we have the answer value
      if (!values || !values.answer) return;

      if (autoSaveTimeout) {
        clearTimeout(autoSaveTimeout);
      }

      const timeout = setTimeout(async () => {
        try {
          const response = await api.post(
            `/qa/submissions`,
            {
              answer: values.answer?.answer || values.answer,
              setSequence: data?.setSequence,
            },
            { showSuccessToast: false }
          );
        } catch (error) {
          console.error('Auto-save error:', error);
        }
      }, 300);
      setAutoSaveTimeout(timeout);

      // Only refetch and update UI state on explicit submission, not auto-save
      if (!values._autoSave) {
        refetch();
        setIsSubmitted(false);
      }
    } catch (error) {
      console.error('Error updating submission:', error);
    }
  };

  // Handle component unmount - save draft
  useEffect(() => {
    return () => {
      setTimeout(() => {
        if (value && !isSubmitted && data?.id) {
          handleUpdate({
            answer: value,
            _autoSave: true,
          });
        }
      }, 300);
    };
  }, [value, data?.id]);

  return (
    <div className="relative">
      <div className="max-w-7xl mx-auto px-5 xl:px-0 relative z-10">
        <GoBack title={'HEC Q & A'} linkClass="my-5 mb-8 w-full max-w-40" />

        <div className="p-5 rounded-lg bg-[#FFF9FB] shadow-lg space-y-5 mb-10">
          <div className="p-5 bg-[#EDFDFD] w-full rounded-lg shadow-lg space-y-5 relative">
            <div className="p-5 bg-[#FCF8EF] rounded-lg [box-shadow:2px_2px_12px_0px_#F5D1B066_inset,_-2px_-2px_12px_0px_#F5D1B066_inset]  flex items-start justify-between">
              <div className="space-y-3">
                <div className="text-xl text-yellow-800 font-semibold">
                  Questions:
                  {data?.assignments?.map((item, index) => (
                    <div key={index}>
                      <h1 className="text-xl pl-4 text-yellow-800">
                        <span className="text-lg">{index + 1})</span>{' '}
                        {item?.question?.question}
                      </h1>
                    </div>
                  ))}
                </div>
                <div>
                  <p className="text-xl text-yellow-800 font-semibold">
                    Instruction:
                  </p>
                  <EditorViewer
                    data={data?.instructions || data?.description}
                  />
                </div>
              </div>
            </div>
          </div>

          {showSubmission ? (
            <div className="space-y-3">
              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  My Submission
                </h1>
                <EditorViewer
                  data={
                    (
                      data?.submissions
                        ?.map((item) => item?.answer)
                        .join(' ') || ''
                    ).length > 200
                      ? data?.submissions
                          ?.map((item) => item?.answer)
                          .join(' ')
                          .slice(0, 400) + '...'
                      : data?.submissions?.map((item) => item?.answer).join(' ')
                  }
                />

                {data?.submission?.status !== 'submitted' && (
                  <div className="absolute right-2 top-2">
                    <ButtonIcon
                      icon={'ri:edit-2-fill'}
                      innerBtnCls={'h-10 w-10'}
                      btnIconCls={'h-5 w-5'}
                      onClick={() => setIsSubmitted(true)}
                    />
                  </div>
                )}
              </div>

              <div className="p-5 shadow-lg rounded-lg border relative bg-white">
                <h1 className="text-xl text-yellow-800 font-semibold">
                  Tutor Correction Zone
                </h1>
                {showFeedback && (
                  <>
                    {data?.submission?.corrections?.grammar?.length > 0 && (
                      <div className="rounded-md">
                        <ul className=" text-sm text-gray-800">
                          {data.submission.corrections.grammar.map(
                            (item, index) => (
                              <EditorViewer key={index} data={item} />
                            )
                          )}
                        </ul>
                      </div>
                    )}

                    <div className="absolute right-2 top-2">
                      <ButtonIcon
                        icon={'arcticons:feedback-2'}
                        innerBtnCls={'h-10 w-10'}
                        btnIconCls={'h-4 w-4'}
                        onClick={() => setShowDetailsModal(true)}
                      />
                    </div>
                  </>
                )}

                <p
                  className={`${
                    !(data?.submission?.status === 'reviewed') && 'text-red-600'
                  } text-center mt-2`}
                >
                  {!(data?.submission?.status === 'reviewed') &&
                    'Not Confirmed yet'}
                </p>
              </div>
            </div>
          ) : (
            <Formik
              initialValues={{
                assignmentId: data?.id,
                answer:
                  value ||
                  data?.submissions?.map((item) => item?.answer).join(' ') ||
                  '',
              }}
              onSubmit={
                data?.submissions?.map((item) => item?.answer).join(' ')
                  ? handleUpdate
                  : handleSubmit
              }
              enableReinitialize
            >
              {() => (
                <Form>
                  <TinyMceEditor
                    name="answer"
                    editorRef={editorRef}
                    initialValue={
                      data?.submissions
                        ?.map((item) => item?.answer)
                        .join(' ') || ''
                    }
                    onAutoSave={(answer) => {
                      setValue(answer);
                      const newWordCount = countWords(answer?.answer || answer);
                      setWordCount(newWordCount);
                      // Set new timeout for auto-save
                      setTimeout(() => {
                        handleUpdate({
                          answer: answer,
                          _autoSave: true,
                        });
                      }, 300); // 0.3 second delay after typing stops
                    }}
                    setValue={setValue}
                    minWords={data?.question?.minimumWords}
                    maxWords={data?.question?.maxWords || Infinity}
                  />

                  <p
                    className={`text-right text-sm mt-2 ${
                      wordCount < data?.question?.minimumWords ||
                      wordCount > data?.question?.maxWords
                        ? 'text-red-500'
                        : 'text-green-600'
                    }`}
                  >
                    {wordCount < data?.question?.minimumWords &&
                      `Minimum ${data?.question?.minimumWords} words required to submit.`}
                    {/* {(wordCount > data?.task?.wordLimitMaximum) &&
                      `Maximum ${data?.task?.wordLimitMaximum} words allowed.`} */}
                  </p>

                  <div className="flex justify-center mt-14 gap-3">
                    <Button
                      buttonText="Cancel"
                      type="button"
                      onClick={() => router.back()}
                    />
                    <Button
                      buttonText={
                        data?.submission?.answer ? 'Update' : 'Submit'
                      }
                      type="submit"
                      className="bg-yellow-400 hover:bg-yellow-500 text-black"
                      disabled={
                        wordCount < data?.task?.wordLimitMinimum ||
                        wordCount > data?.task?.wordLimitMaximum
                      }
                    />
                  </div>
                </Form>
              )}
            </Formik>
          )}
        </div>
      </div>

      <DetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        data={data?.submission?.corrections?.grammar}
        title="Teachers Feedback"
        link={`/answer`}
        showBtn={false}
      />
    </div>
  );
};

export default HecLatestAnswer;
