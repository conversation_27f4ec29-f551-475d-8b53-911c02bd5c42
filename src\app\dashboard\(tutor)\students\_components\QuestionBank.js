'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation } from '@tanstack/react-query';
import { toast } from 'sonner';
import NewTablePage from '@/components/form/NewTablePage';
import useDataFetch from '@/hooks/useDataFetch';
import api from '@/lib/api';

const QuestionBank = ({ student, onBack }) => {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedRowIndices, setSelectedRowIndices] = useState([]);
  const [showModal, setShowModal] = useState(false);
  const [instructions, setInstructions] = useState('');

  const { data: apiData, isLoading, error } = useDataFetch({
    queryKey: ['tutor-qa-questions', currentPage, rowsPerPage],
    endPoint: '/tutor/qa/questions',
    params: { page: currentPage, limit: rowsPerPage }
  });

  // Mutation for creating assignment
  const createAssignmentMutation = useMutation({
    mutationFn: async (assignmentData) => {
      const response = await api.post('/tutor/qa/create-assignment', assignmentData);
      return response;
    },
    onSuccess: () => {
      toast.success('Questions assigned successfully!');
      setShowModal(false);
      setInstructions('');
      setSelectedRowIndices([]);
      onBack();
    },
    onError: (error) => {
      const errorMessage = error?.response?.data?.message || 'Failed to assign questions';
      toast.error(errorMessage);
    }
  });

  const payload = apiData?.data ?? apiData ?? {};
  const items = payload.items ?? [];
  const totalItems = payload.totalItems ?? items.length;
  const totalPages = payload.totalPages ?? 1;

  const questions = items.map(q => ({
    id: q.id,
    title: q.question,
    points: q.points,
    minimumWords: q.minimumWords
  }));

  const columns = [
    { label: 'QUESTION TITLE', field: 'title' },
    { label: 'POINTS', field: 'points' },
    { label: 'MINIMUM WORDS', field: 'minimumWords' }
  ];

  

  const handleDoneClick = () => {
    if (selectedRowIndices.length === 0) {
      toast.error('Please select at least one question');
      return;
    }
    setShowModal(true);
  };

  const handleAssignQuestions = () => {
    if (!instructions.trim()) {
      toast.error('Please provide instructions');
      return;
    }

    const questionIds = selectedRowIndices.map(i => questions[i]?.id).filter(Boolean);

    const assignmentData = {
      questionIds,
      studentId: student.id,
      instructions: instructions.trim()
    };

    createAssignmentMutation.mutate(assignmentData);
  };

  return (
    <div className="bg-white">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold">Question Bank</h2>
        <div className="flex gap-3">
          <button
            onClick={handleDoneClick}
            disabled={selectedRowIndices.length === 0}
            className={`px-4 py-2 font-medium rounded-lg transition-colors ${
              selectedRowIndices.length > 0
                ? 'bg-yellow-400 hover:bg-yellow-500 text-black'
                : 'bg-gray-500 text-white cursor-not-allowed'
            }`}
          >
            Done
          </button>
        </div>
      </div>
      <NewTablePage
      title=""
        columns={columns}
        data={questions}
        currentPage={currentPage}
        changePage={setCurrentPage}
        totalItems={totalItems}
        rowsPerPage={rowsPerPage}
        setRowsPerPage={setRowsPerPage}
        totalPages={totalPages}
        loading={isLoading}
        error={!!error}
        errorMessage={error}
        showCheckboxes
        showSearch={false}
        showNameFilter={false}
        showSortFilter={false}
        showCreateButton={false}
        onRowSelectionChange={setSelectedRowIndices}
      />

      {/* Instructions Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold mb-4">Assignment Instructions</h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Instructions for the student:
              </label>
              <textarea
                value={instructions}
                onChange={(e) => setInstructions(e.target.value)}
                placeholder="Please focus on SOLID principles in your answer"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-400 focus:border-transparent resize-none"
                rows={4}
              />
            </div>
            <div className="flex gap-3 justify-end">
              <button
                onClick={() => {
                  setShowModal(false);
                  setInstructions('');
                }}
                className="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAssignQuestions}
                disabled={createAssignmentMutation.isPending}
                className="px-4 py-2 bg-yellow-400 hover:bg-yellow-500 text-black font-medium rounded-lg transition-colors disabled:opacity-50"
              >
                {createAssignmentMutation.isPending ? 'Assigning...' : 'Assign Questions'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionBank;
