/** @type {import('next').NextConfig} */

// Import environment variables from config
const HOST_CONFIG = process.env.NEXT_PUBLIC_API_HOST || '**************';
const PORT_CONFIG = process.env.NEXT_PUBLIC_API_PORT || '3010'; // fallback to 80 if not set

const nextConfig = {
  reactStrictMode: true, // Helps catch potential React issues

  images: {
    remotePatterns: [
      {
        protocol: 'http',
        hostname: HOST_CONFIG,
        port: PORT_CONFIG,
        pathname: '/**',
      },
    ],
  },

  // Optimize handling of server-side modules in client-side code
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        fs: false,
        path: false,
        os: false,
      };
    }
    return config;
  },

  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          { key: 'X-Frame-Options', value: 'SAMEORIGIN' },
          { key: 'X-Content-Type-Options', value: 'nosniff' },
          { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },

  // Redirects: Helps with URL changes
  async redirects() {
    return [
      {
        source: '/old-path',
        destination: '/new-path',
        permanent: true,
      },
    ];
  },

  // Rewrites: Useful for proxying API requests
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `http://${HOST_CONFIG}:${PORT_CONFIG}/api/:path*`,
      },
    ];
  },

  // Environment variables are now managed in src/lib/config.js
};

module.exports = nextConfig;
