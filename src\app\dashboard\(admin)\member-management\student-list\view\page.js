'use client';
import React, { useState } from 'react';
import { X, User, Mail, Phone, Calendar, MapPin, Users, Maximize2, Minimize2, GraduationCap, BookOpen, CreditCard, Award, Settings, ChevronRight } from 'lucide-react';

const StudentViewModal = ({ isOpen, onClose, studentData }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [selectedFeature, setSelectedFeature] = useState(null);
  
  if (!isOpen || !studentData) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'Not provided';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const calculateAge = (birthDate) => {
    if (!birthDate) return null;
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  };

  const getAgeDisplay = () => {
    // If age is provided, use it
    if (studentData.age) {
      return studentData.dateOfBirth 
        ? `${studentData.age} (Born: ${formatDate(studentData.dateOfBirth)})`
        : studentData.age;
    }
    
    // If age is not provided but birthdate is, calculate age
    if (studentData.dateOfBirth) {
      const calculatedAge = calculateAge(studentData.dateOfBirth);
      return `${calculatedAge} (Born: ${formatDate(studentData.dateOfBirth)})`;
    }
    
    return 'Not provided';
  };

  const getGenderDisplay = (gender) => {
    if (!gender) return 'Not specified';
    return gender.charAt(0).toUpperCase() + gender.slice(1);
  };

  // Get HEC features from API data - using real data only
  const getHecFeatures = () => {
    if (!studentData?.activePlanDetails?.plan?.legacyFeatures) {
      return [];
    }

    return studentData.activePlanDetails.plan.legacyFeatures.map((feature) => {
      // Use only real API data, no mock details
      const realFeatureDetails = {
        'Feature Status': feature.isActive ? 'Active' : 'Inactive',
        'Feature Type': feature.type || 'Not specified',
        'Feature Name': feature.name || 'Not specified',
        'Description': feature.description || 'No description available'
      };

      // Add any additional properties that exist in the API response
      if (feature.createdAt) {
        realFeatureDetails['Created At'] = formatDate(feature.createdAt);
      }
      if (feature.updatedAt) {
        realFeatureDetails['Updated At'] = formatDate(feature.updatedAt);
      }
      if (feature.hasOwnProperty('isEnabled')) {
        realFeatureDetails['Enabled'] = feature.isEnabled ? 'Yes' : 'No';
      }
      if (feature.permissions && Array.isArray(feature.permissions)) {
        realFeatureDetails['Permissions'] = feature.permissions.join(', ');
      }
      if (feature.limits) {
        realFeatureDetails['Limits'] = JSON.stringify(feature.limits);
      }
      if (feature.settings) {
        realFeatureDetails['Settings'] = JSON.stringify(feature.settings);
      }

      return {
        id: feature.type || feature.id || Math.random().toString(36).substr(2, 9),
        name: feature.name || 'Unnamed Feature',
        description: feature.description || 'No description available',
        isActive: feature.isActive || false,
        details: realFeatureDetails,
        rawData: feature // Keep raw data for debugging
      };
    });
  };

  const hecFeatures = getHecFeatures();

  const modalSize = isExpanded 
    ? "max-w-6xl w-full max-h-[95vh]" 
    : "max-w-4xl w-full max-h-[80vh]";

  const renderProfileTab = () => (
    <>
      {/* Personal and Contact Details - Side by Side */}
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Personal Details */}
        <div className="flex-1">
          <h3 className="text-lg font-medium text-[#723F11] mb-4">Personal Details</h3>
          <div className="space-y-4">
            {/* Biography */}
            <div>
              <label className="block text-sm font-medium text-black mb-1">Biography</label>
              <div className="p-3 bg-gray-50 rounded-md text-sm text-gray-600">
                {studentData?.bio || 'No biography provided'}
              </div>
            </div>

            {/* Age */}
            <div>
              <label className="block text-sm font-medium text-black mb-1">Age</label>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-900">
                  {getAgeDisplay()}
                </span>
              </div>
            </div>

            {/* Gender */}
            <div>
              <label className="block text-sm font-medium text-black mb-1">Gender</label>
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-900">{getGenderDisplay(studentData?.gender)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Dashed line separator */}
        <div className="hidden lg:block w-px border-l-2 border-dashed border-gray-300 mx-4"></div>

        {/* Contact Details */}
        <div className="flex-1">
          <h3 className="text-lg font-medium text-[#723F11] mb-4">Contact Details</h3>
          <div className="space-y-4">
            {/* Address */}
            <div>
              <label className="block text-sm font-medium text-black mb-1">Address</label>
              <div className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                <div className="text-sm text-gray-900">
                  {(() => {
                    const addressParts = [
                      studentData?.address,
                      studentData?.city,
                      studentData?.state,
                      studentData?.country,
                      studentData?.postalCode
                    ].filter(Boolean);
                    
                    return addressParts.length > 0 
                      ? addressParts.join(', ')
                      : 'No address provided';
                  })()}
                </div>
              </div>
            </div>

            {/* Phone Number */}
            <div>
              <label className="block text-sm font-medium text-black mb-1">Phone Number</label>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-900">
                  {studentData?.phoneNumber || 'Not provided'}
                </span>
              </div>
            </div>

            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-black mb-1">Email</label>
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-900">{studentData?.email}</span>
              </div>
            </div>

            {/* User ID */}
            <div>
              <label className="block text-sm font-medium text-black mb-1">User ID</label>
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-900">{studentData?.userId}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dotted line separator */}
      <div className="my-8">
        <div className="border-t-2 border-dotted border-gray-300"></div>
      </div>

      {/* Learning Information */}
      <div>
        <h3 className="text-lg font-medium text-[#723F11] mb-4">Learning Information</h3>
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
          {/* Assigned Tutors */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-blue-800 flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Assigned Tutors
            </div>
            <div className="text-sm text-blue-600 mt-1">
              {studentData?.assignedTutorCount || 0} tutors
            </div>
          </div>

          {/* Assigned Modules */}
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-green-800 flex items-center">
              <BookOpen className="h-4 w-4 mr-2" />
              Enrolled Modules
            </div>
            <div className="text-sm text-green-600 mt-1">
              {studentData?.assignedModuleCount || 0} modules
            </div>
          </div>
        </div>

        {/* Assigned Tutors List */}
        {studentData?.assignedTutors && studentData.assignedTutors.length > 0 && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">Assigned Tutors & Modules</h4>
            <div className="space-y-3">
              {studentData.assignedTutors.map((assignment, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded-md">
                  <div className="text-sm font-medium text-gray-900">{assignment.tutorName}</div>
                  <div className="text-sm text-gray-600">{assignment.moduleName}</div>
                  <div className="text-xs text-gray-500">Tutor ID: {assignment.tutorId}</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Dotted line separator */}
      <div className="my-8">
        <div className="border-t-2 border-dotted border-gray-300"></div>
      </div>

      {/* Subscription Information */}
      <div>
        <h3 className="text-lg font-medium text-[#723F11] mb-4">Subscription Information</h3>
        
        {/* Active Plan Overview */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 p-4 rounded-lg mb-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="text-lg font-semibold text-purple-800 flex items-center">
                <Award className="h-5 w-5 mr-2" />
                {studentData?.activePlan || 'No Active Plan'}
              </div>
              {studentData?.activePlanDetails?.plan?.description && (
                <div className="text-sm text-purple-600 mt-1">
                  {studentData.activePlanDetails.plan.description}
                </div>
              )}
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-purple-800">
                ₩{studentData?.activePlanDetails?.plan?.price || '0'}
              </div>
              <div className="text-sm text-purple-600">
                /{studentData?.activePlanDetails?.plan?.subscriptionType || 'year'}
              </div>
            </div>
          </div>
        </div>

        {/* Plan Details Grid */}
        {studentData?.activePlanDetails && (
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-green-800 flex items-center">
                <CreditCard className="h-4 w-4 mr-2" />
                Payment Status
              </div>
              <div className="text-sm text-green-600">
                {studentData.activePlanDetails.isPaid ? 'Paid' : 'Unpaid'}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-blue-800">Start Date</div>
              <div className="text-sm text-blue-600">
                {formatDate(studentData.activePlanDetails.startDate)}
              </div>
            </div>
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-sm font-medium text-orange-800">End Date</div>
              <div className="text-sm text-orange-600">
                {formatDate(studentData.activePlanDetails.endDate)}
              </div>
            </div>
          </div>
        )}

        {/* Plan Features */}
        {studentData?.activePlanDetails?.plan?.legacyFeatures && studentData.activePlanDetails.plan.legacyFeatures.length > 0 && (
          <div className="mt-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">Included Features</h4>
            <div className="grid gap-2 grid-cols-1 sm:grid-cols-2">
              {studentData.activePlanDetails.plan.legacyFeatures.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 bg-gray-50 p-2 rounded">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-700">{feature.name}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Dotted line separator */}
      <div className="my-8">
        <div className="border-t-2 border-dotted border-gray-300"></div>
      </div>

      {/* Account Status */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Account Status</h3>
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-green-800">Status</div>
            <div className="text-sm text-green-600">
              {studentData?.isActive ? 'Active' : 'Inactive'}
            </div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-blue-800">Confirmed</div>
            <div className="text-sm text-blue-600">
              {studentData?.isConfirmed ? 'Yes' : 'No'}
            </div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="text-sm font-medium text-purple-800">Role</div>
            <div className="text-sm text-purple-600">
              {studentData?.selectedRole || 'Student'}
            </div>
          </div>
        </div>
      </div>

      {/* Dotted line separator */}
      <div className="my-8">
        <div className="border-t-2 border-dotted border-gray-300"></div>
      </div>

      {/* Timestamps */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Activity Information</h3>
        <div className="grid gap-4 text-sm grid-cols-1">
          <div>
            <span className="font-medium text-gray-700">Created At:</span>
            <span className="ml-2 text-gray-600">{formatDate(studentData?.createdAt)}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Last Updated:</span>
            <span className="ml-2 text-gray-600">{formatDate(studentData?.updatedAt)}</span>
          </div>
          {studentData?.lastLoginAt && (
            <div>
              <span className="font-medium text-gray-700">Last Login:</span>
              <span className="ml-2 text-gray-600">{formatDate(studentData.lastLoginAt)}</span>
            </div>
          )}
        </div>
      </div>
    </>
  );

  const renderHECTab = () => (
    <div className="flex h-full">
      {/* Sidebar with features */}
      <div className="w-1/3 border-r border-gray-200 pr-4">
        <h3 className="text-lg font-medium text-[#723F11] mb-4">HEC Features</h3>
        <div className="space-y-2">
          {hecFeatures.length > 0 ? (
            hecFeatures.map((feature) => (
              <button
                key={feature.id}
                onClick={() => setSelectedFeature(feature)}
                className={`w-full text-left p-3 rounded-lg transition-colors flex items-center justify-between ${
                  selectedFeature?.id === feature.id
                    ? 'bg-[#FEFCE8] border border-yellow-100 text-[#723F11]'
                    : 'bg-gray-50 hover:bg-[#FEFCE8] text-gray-700'
                }`}
              >
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <div className="font-medium text-sm">{feature.name}</div>
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      feature.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {feature.isActive ? 'Active' : 'Inactive'}
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">{feature.description}</div>
                </div>
                <ChevronRight className="h-4 w-4 ml-2" />
              </button>
            ))
          ) : (
            <div className="text-center text-gray-500 py-8">
              <p>No HEC features available</p>
            </div>
          )}
        </div>
      </div>

      {/* Details panel */}
      <div className="flex-1 pl-6">
        {selectedFeature ? (
          <div>
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-gray-900">{selectedFeature.name}</h3>
              <p className="text-gray-600 mt-1">{selectedFeature.description}</p>
            </div>

            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
              {Object.entries(selectedFeature.details).map(([key, value], index) => (
                <div key={index} className="bg-white border border-gray-200 p-4 rounded-lg">
                  <div className="text-sm font-medium text-gray-700 mb-1">{key}</div>
                  <div className="text-lg font-semibold text-gray-900">{value}</div>
                </div>
              ))}
            </div>


            {/* Additional information section */}
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="text-sm text-black">
                <strong>Plan Information:</strong> This feature is included in {studentData?.activePlan || 'current plan'}. 
                {selectedFeature?.isActive 
                  ? ' Feature is currently active and accessible.'
                  : ' Please contact support if you need to activate this feature.'
                }
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-64">
            <div className="text-center text-gray-500">
              <Settings className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-lg font-medium">Select a feature</p>
              <p className="text-sm">Choose a feature from the sidebar to view detailed information</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white rounded-lg shadow-xl ${modalSize} flex flex-col`}>
        {/* Header with tabs */}
        <div className="flex flex-col border-b border-gray-200 flex-shrink-0">
          {/* Top header */}
          <div className="flex items-center justify-between p-6">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 text-[#FEFCE8]">
                <GraduationCap className="h-8 w-8" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">{studentData?.name || 'Student'}</h2>
                <p className="text-sm text-gray-500">User ID: {studentData?.userId}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                title={isExpanded ? "Minimize" : "Expand"}
              >
                {isExpanded ? (
                  <Minimize2 className="h-5 w-5 text-gray-500" />
                ) : (
                  <Maximize2 className="h-5 w-5 text-gray-500" />
                )}
              </button>
              <button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="h-5 w-5 text-gray-500" />
              </button>
            </div>
          </div>

          {/* Tab navigation */}
          <div className="px-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => {
                  setActiveTab('profile');
                  setSelectedFeature(null);
                }}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'profile'
                    ? 'border-yellow-300 text-black hover:bg-[#FEFCE8] '
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Student Profile
              </button>
              <button
                onClick={() => setActiveTab('hec')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'hec'
                     ? 'border-yellow-300 text-black hover:bg-[#FEFCE8] '
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                HEC Information
              </button>
            </nav>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {activeTab === 'profile' ? renderProfileTab() : renderHECTab()}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50 flex-shrink-0">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default StudentViewModal;